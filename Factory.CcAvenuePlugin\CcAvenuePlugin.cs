using System;
using System.Collections.Generic;
using System.Dynamic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Factory.Models;
using Factory.Plugins;
using CCA.Util;

namespace Factory.CcAvenuePlugin
{
    public class CcAvenuePlugin : IRemoteCallPlugin
    {
        public string GetName()
        {
            return "CcAvenue";
        }

        public List<string> GetMethods()
        {
            return new List<string> { "Generate" };
        }

        public string GetHelp()
        {
            return "";
        }

        public List<FieldMeta> GetConfigurationMeta()
        {
            return new List<FieldMeta>();
        }

        public void Configure(dynamic configuration)
        {
        }

        public Task<dynamic> Invoke(dynamic input)
        {
            dynamic output = new ExpandoObject();
            output.Result = new CcAvenue(Mapper.Map<Model>(input)).Compose();
            return Task.FromResult<dynamic>(output);
        }
    }

    public class CcAvenue
    {
        private readonly Model _model;

        public CcAvenue(Model model)
        {
            _model = model;
        }

        public string Compose()
        {
            try
            {
                CCACrypto ccaCrypto = new CCACrypto();
                if (!String.IsNullOrWhiteSpace(_model.strToEncrypt))
                {
                    return ccaCrypto.Encrypt(_model.strToEncrypt, _model.Key);
                }
                return ccaCrypto.Decrypt(_model.strToDecrypt, _model.Key);
            }
            catch (Exception ez)
            {
                Console.WriteLine($"Error: {ez.Message}");
                return null;
            }
        }
    }

    public static class Mapper
    {
        public static T Map<T>(ExpandoObject source) where T : class
        {
            T destination = (T)Activator.CreateInstance(typeof(T));
            IDictionary<string, object> dict = source;
            var type = destination.GetType();

            foreach (var prop in type.GetProperties())
            {
                var lower = prop.Name.ToLower();
                Console.WriteLine($"Mapping {lower}");
                var key = dict.Keys.SingleOrDefault(k => k.ToLower() == lower);

                if (key != null)
                {
                    prop.SetValue(destination, CastProperty(dict[key], prop.PropertyType), null);
                }
            }
            return destination;
        }

        static dynamic CastProperty(dynamic property, Type type)
        {
            try
            {
                return Convert.ChangeType(property, type);
            }
            catch (Exception)
            {
                return null;
            }
        }
    }

    public class Model
    {
        public string strToDecrypt { get; set; }
        public string strToEncrypt { get; set; }
        public string Key { get; set; }
    }
}
