using QuestPDF.Fluent;
using QuestPDF.Infrastructure;
using QuestPDF.Drawing;
using Svg.Skia;
using Factory.Plugins;
using Factory.Models;
using System.Reflection;
using System.Dynamic;
using QuestPDF.Helpers;
using SkiaSharp;
using static Factory.HoadingSelfDeclarationPlugin.Mapper;

namespace Factory.HoadingSelfDeclarationPlugin
{
    public class HoadingSelfDeclarationPlugin : IRemoteCallPlugin
    {
        public string GetName()
        {
            return "HoadingSelfDeclarationForm";
        }

        public List<string> GetMethods()
        {
            return new List<string>() {
                "Generate"
            };
        }

        public string GetHelp()
        {
            return "";
        }

        public List<FieldMeta> GetConfigurationMeta()
        {
            return new List<FieldMeta>() { };
        }



        public void Configure(dynamic configuration)
        {
        }

        public Task<dynamic> Invoke(dynamic input)
        {

            dynamic output = new ExpandoObject();
            output.Result = new HoadingSelfDeclaration(Mapper.Map<Model>(input)).GeneratePdf();
            return Task.FromResult<dynamic>(output);
        }
    }

    public class HoadingSelfDeclaration : IDocument
    {
        static HoadingSelfDeclaration()
        {
            var mangalFont = Assembly.GetExecutingAssembly().GetManifestResourceStream("Factory.HoadingSelfDeclarationPlugin.Mangal.ttf");
            if (mangalFont != null)
            {
                FontManager.RegisterFont(mangalFont);
            }
            var timesFont = Assembly.GetExecutingAssembly().GetManifestResourceStream("Factory.HoadingSelfDeclarationPlugin.times.ttf");
            if (timesFont != null)
            {
                FontManager.RegisterFont(timesFont);
            }
        }



        public Model _model { get; }

        public HoadingSelfDeclaration(Model model)
        {
            _model = model;
        }


        public DocumentMetadata GetMetadata() => DocumentMetadata.Default;

        public void Compose(IDocumentContainer container)
        {
            container
                .Page(page =>
                {
                    page.Margin(30);
                    page.Header().Element(ComposeHeader);
                    page.Content().Element(ComposeContent);


                });
        }
        void ComposeHeader(IContainer container)
        {
            container.Column(column =>
            {
                var stream = Assembly.GetExecutingAssembly().GetManifestResourceStream("Factory.HoadingSelfDeclarationPlugin.Logo.png");
                if (stream != null)
                {

                    column.Item().Row(row =>
                    {
                        row.RelativeItem(4);
                        row.RelativeItem(1)
                            .AlignCenter()
                            .Height(50)
                            .Image(new BinaryReader(stream).ReadBytes((int)stream.Length), ImageScaling.Resize);
                        row.RelativeItem(4);
                    });
                }
                column.Item()
                    .AlignCenter()
                    .Text(text =>
                    {

                        text.Span("प्रतिज्ञापत्र").FontSize(10)
                            .FontFamily("Mangal")
                            .FontSize(15).Bold();
                    });
            });

        }
        void ComposeContent(IContainer container)
        {
            container.Column(column =>
            {
                column.Item().PaddingTop(50).Text(text =>
               {
                   text.Span($" या अर्जाद्वारे आम्ही मागणी केलेली जाहिरात फलकाची परवानगी ही जाहिरात फलक नियंत्रण नियमावली, 2003 च्या धोरणास अनुसरून आहे. या जाहिरात धोरणाचे उल्लंघन झाल्यास सदरची परवानगी केव्हाही रद्द केली जाईल याची मला जाणीव आहे व वरील माहिती माझ्या माहिती व ज्ञानाप्रमाणे सत्य आहे. ती खोटी आढळल्यास माझ्या जाहिरात बालकाची परवानगी रद्द होईल हे मला मान्य आहे.")
                          .FontFamily("Mangal")
                       .FontSize(10);
               });
                column.Item().Row(row =>
               {
                   row.RelativeItem(1)
                           .AlignRight().Text(text =>
                           {
                               text.Span("अर्जदाराची स्वाक्षरी")
                               .FontFamily("Mangal")
                           .FontSize(10);
                           });
               });
            });
        }
    }

    public static class Mapper
    {
        public static T Map<T>(ExpandoObject source) where T : class
        {
            T destination = (T)Activator.CreateInstance(typeof(T));
            IDictionary<string, object> dict = source;
            var type = destination.GetType();

            foreach (var prop in type.GetProperties())
            {
                var lower = prop.Name.ToLower();
                Console.WriteLine($"Mapping {lower}");
                var key = dict.Keys.SingleOrDefault(k => k.ToLower() == lower);

                if (key != null)
                {
                    prop.SetValue(destination, CastProperty(dict[key], prop.PropertyType.ToString()), null);
                }
            }
            return destination;
        }

        static dynamic CastProperty(dynamic property, string type)
        {
            switch (type)
            {
                case "System.String":
                    return (String)property;
                case "System.Decimal":
                    return (Decimal)property;
                default:
                    return property;

            }
        }
        public class Model
        {
            public string Name { get; set; }
            public string FatherName { get; set; }
            public string Position { get; set; }
            public string Age { get; set; }
            public string Address { get; set; }
            public string Date { get; set; }
        }


    }
}
