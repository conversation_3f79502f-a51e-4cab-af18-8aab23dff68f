using QuestPDF.Fluent;
using QuestPDF.Infrastructure;
using QuestPDF.Drawing;
using Svg.Skia;
using Factory.Plugins;
using Factory.Models;
using System.Reflection;
using System.Dynamic;
using QuestPDF.Helpers;
using SkiaSharp;
using static Factory.ChallanPlugin.Mapper;

namespace Factory.ChallanPlugin
{
    public class ChallanPlugin : IRemoteCallPlugin
    {
        public string GetName()
        {
            return "Challan";
        }

        public List<string> GetMethods()
        {
            return new List<string>() {
                "Generate"
            };
        }

        public string GetHelp()
        {
            return "";
        }

        public List<FieldMeta> GetConfigurationMeta()
        {
            return new List<FieldMeta>() { };
        }



        public void Configure(dynamic configuration)
        {
        }

        public Task<dynamic> Invoke(dynamic input)
        {
            dynamic output = new ExpandoObject();
            output.Result = new Challan(Mapper.Map<Model>(input)).GeneratePdf();
            return Task.FromResult<dynamic>(output);
        }
    }

    public class Challan : IDocument
    {
        static Challan()
        {
            var mangalFont = Assembly.GetExecutingAssembly().GetManifestResourceStream("Factory.ChallanPlugin.Mangal.ttf");
            if (mangalFont != null)
            {
                FontManager.RegisterFont(mangalFont);
            }
            var timesFont = Assembly.GetExecutingAssembly().GetManifestResourceStream("Factory.ChallanPlugin.times.ttf");
            if (timesFont != null)
            {
                FontManager.RegisterFont(timesFont);
            }
        }


        public Model _model { get; }

        public Challan(Model model)
        {
            _model = model;
        }

        public DocumentMetadata GetMetadata() => DocumentMetadata.Default;

        public void Compose(IDocumentContainer container)
        {
            container
                .Page(page =>
                {
                    page.Margin(20);
                    page.Header().Element(ComposeHeader);
                    page.Content().Element(ComposeContent);


                });
        }
        void ComposeHeader(IContainer container)
        {
            container.Column(column =>
            {
                var stream = Assembly.GetExecutingAssembly().GetManifestResourceStream("Factory.ChallanPlugin.Logo.png");
                if (stream != null)
                {

                    // column.Item().Row(row =>
                    // {
                    //     row.RelativeItem(4);
                    //     row.RelativeItem(1)
                    //         .AlignCenter()
                    //         .Height(50)
                    //         .Image(new BinaryReader(stream).ReadBytes((int)stream.Length), ImageScaling.Resize);
                    //     row.RelativeItem(4);
                    // });
                }
                // column.Item()
                //     .AlignCenter()
                //     .Text(text =>
                //     {

                //         text.Span("स्वयं-साक्षांकनासाठी स्वयं घोषणापत").FontSize(10)
                //             .FontFamily("Mangal")
                //             .FontSize(10).Bold();
                //     });
            });

        }
        void ComposeContent(IContainer container)
        {
            container.Row(column =>
            {
                var stream = Assembly.GetExecutingAssembly().GetManifestResourceStream("Factory.ChallanPlugin.Logo.png");
                if (stream != null)
                {

                }

                column.RelativeItem()
               .AlignCenter()
               .MinimalBox()
               .Border(1)
               .Padding(3)
               .Width(4 * 35) // sizes from 80x40 to 240x120
               .Height(22 * 17)
               .ScaleToFit()
               .Text(text =>
{
    text.DefaultTextStyle(x => x.FontSize(8).FontFamily("Mangal").LineHeight(2));
    text.Element().Height(24).Width(48).Image(new BinaryReader(stream).ReadBytes((int)stream.Length), ImageScaling.Resize);
    text.Line(" PUNE MUNICIPAL CORPORATION").Underline().FontFamily("Times New Roman").FontSize(8).Bold();
    text.Line("               चलन पावती").FontSize(8).Bold();
    text.Line("फाईल/संदर्भ");
    text.Span($"अर्ज क्र :");
    text.Line($" LIC01").FontFamily("Times New Roman");
    text.Span("चलन क्र : ");
    text.Line($" {_model.ChallanNumber}");
    text.Span("खात्याचे नाव :");
    text.Line($" {_model.Position}").FontFamily("Times New Roman");
    text.Span("आर्किटेक्ट नाव :");
    text.Line($" ");
    text.Span("मालकाचे नाव :");
    text.Line($" {_model.Name}").FontFamily("Times New Roman");
    text.Span("मिळकत :");
    text.Line($"NEW LICENSE ENGG").FontFamily("Times New Roman");
    text.Line("General").FontFamily("Times New Roman").LineHeight(0);
    text.Line("___________________________________________").LineHeight(1);
    text.Line("  अर्थशिर्षक           तपशील      रक्कमरुपये").LineHeight(0);
    text.Line("___________________________________________").LineHeight(0);
    text.Line($"LicensedEngineer(G)   R123A102          {_model.Amount}.00").FontFamily("Times New Roman");
    text.Line($"                                  {_model.Amount}.00");
    // text.EmptyLine().LineHeight(2);
    text.Line("___________________________________________").LineHeight(0);
    text.Line("एकूण रक्कम रुपये (अक्षरी)").LineHeight(1);
    text.Line($"{_model.AmountInWords}").FontFamily("Times New Roman").FontSize(8).ExtraBold().LineHeight(0);
    text.Line("___________________________________________").LineHeight(0);
    text.Span("Challan Date.").FontFamily("Times New Roman");
    text.Line($" {_model.Date.Date}").LineHeight(1);
});
                var stream2 = Assembly.GetExecutingAssembly().GetManifestResourceStream("Factory.ChallanPlugin.Logo.png");
                if (stream2 != null)
                {

                }

                column.RelativeItem()
               .AlignMiddle()
               .MinimalBox()
               .Border(1)
               .Padding(3)
               .Width(4 * 35) // sizes from 80x40 to 240x120
               .Height(22 * 17)
               .ScaleToFit()
               .Text(text =>
{
    text.DefaultTextStyle(x => x.FontSize(8).FontFamily("Mangal").LineHeight(2));
    text.Element().Height(24).Width(48).Image(new BinaryReader(stream2).ReadBytes((int)stream2.Length), ImageScaling.Resize);
    text.Line(" PUNE MUNICIPAL CORPORATION").Underline().FontFamily("Times New Roman").FontSize(8).Bold();
    text.Line("               चलन पावती").FontSize(8).Bold();
    text.Line("फाईल/संदर्भ");
    text.Span($"अर्ज क्र :");
    text.Line($" LIC01").FontFamily("Times New Roman");
    text.Span("चलन क्र : ");
    text.Line($" {_model.ChallanNumber}");
    text.Span("खात्याचे नाव :");
    text.Line($" {_model.Position}").FontFamily("Times New Roman");
    text.Span("आर्किटेक्ट नाव :");
    text.Line($" ");
    text.Span("मालकाचे नाव :");
    text.Line($" {_model.Name}").FontFamily("Times New Roman");
    text.Span("मिळकत :");
    text.Line($"NEW LICENSE ENGG").FontFamily("Times New Roman");
    text.Line("General").FontFamily("Times New Roman").LineHeight(0);
    text.Line("___________________________________________").LineHeight(1);
    text.Line("  अर्थशिर्षक           तपशील      रक्कमरुपये").LineHeight(0);
    text.Line("___________________________________________").LineHeight(0);
    text.Line($"LicensedEngineer(G)   R123A102          {_model.Amount}.00").FontFamily("Times New Roman");
    text.Line($"                                  {_model.Amount}.00");
    // text.EmptyLine().LineHeight(2);
    text.Line("___________________________________________").LineHeight(0);
    text.Line("एकूण रक्कम रुपये (अक्षरी)").LineHeight(1);
    text.Line($"{_model.AmountInWords}").FontFamily("Times New Roman").FontSize(8).ExtraBold().LineHeight(0);
    text.Line("___________________________________________").LineHeight(0);
    text.Span("Challan Date.").FontFamily("Times New Roman");
    text.Line($" {_model.Date.Date}").LineHeight(1);
});





            });
        }
    }

    public static class Mapper
    {
        public static T Map<T>(ExpandoObject source) where T : class
        {
            T destination = (T)Activator.CreateInstance(typeof(T));
            IDictionary<string, object> dict = source;
            var type = destination.GetType();

            foreach (var prop in type.GetProperties())
            {
                var lower = prop.Name.ToLower();
                Console.WriteLine($"Mapping {lower}");
                var key = dict.Keys.SingleOrDefault(k => k.ToLower() == lower);

                if (key != null)
                {
                    prop.SetValue(destination, CastProperty(dict[key], prop.PropertyType.ToString()), null);
                }
            }
            return destination;
        }

        static dynamic CastProperty(dynamic property, string type)
        {
            switch (type)
            {
                case "System.String":
                    return (String)property;
                case "System.Decimal":
                    return (Decimal)property;
                default:
                    return property;

            }
        }
        public class Model
        {
            public string ChallanNumber { get; set; }
            public string Name { get; set; }
            public string Number { get; set; }
            public string Address { get; set; }
            public DateTime Date { get; set; }
            public string Position { get; set; }
            public string Amount { get; set; }
            public string AmountInWords { get; set; }
        }

    }
}
