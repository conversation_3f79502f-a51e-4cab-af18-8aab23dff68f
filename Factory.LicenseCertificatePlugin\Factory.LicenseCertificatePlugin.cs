using QuestPDF.Fluent;
using QuestPDF.Infrastructure;
using QuestPDF.Drawing;
using Svg.Skia;
using Factory.Plugins;
using Factory.Models;
using System.Reflection;
using System.Dynamic;
using QuestPDF.Helpers;
using SkiaSharp;
using static Factory.LicenseCertificatePlugin.Mapper;

namespace Factory.LicenseCertificatePlugin
{
    public class LicenseCertificatePlugin : IRemoteCallPlugin
    {
        public string GetName()
        {
            return "LicenseCertificate";
        }

        public List<string> GetMethods()
        {
            return new List<string>() {
                "Generate"
            };
        }

        public string GetHelp()
        {
            return "";
        }

        public List<FieldMeta> GetConfigurationMeta()
        {
            return new List<FieldMeta>() { };
        }



        public void Configure(dynamic configuration)
        {
        }

        public Task<dynamic> Invoke(dynamic input)
        {

            dynamic output = new ExpandoObject();
            output.Result = new LicenseCertificate(Mapper.Map<Model>(input)).GeneratePdf();
            return Task.FromResult<dynamic>(output);
        }
    }

    public class LicenseCertificate : IDocument
    {
        static LicenseCertificate()
        {
            var mangalFont = Assembly.GetExecutingAssembly().GetManifestResourceStream("Factory.LicenseCertificatePlugin.Mangal.ttf");
            if (mangalFont != null)
            {
                FontManager.RegisterFont(mangalFont);
            }
            var timesFont = Assembly.GetExecutingAssembly().GetManifestResourceStream("Factory.LicenseCertificatePlugin.times.ttf");
            if (timesFont != null)
            {
                FontManager.RegisterFont(timesFont);
            }
        }



        public Model _model { get; }

        public LicenseCertificate(Model model)
        {
            _model = model;
        }


        public DocumentMetadata GetMetadata() => DocumentMetadata.Default;

        public void Compose(IDocumentContainer container)
        {
            container
                .Page(page =>
                {
                    page.Margin(30);
                    page.Header().Element(ComposeHeader);
                    page.Content().Element(ComposeContent);
                    // page.Content().Element(ComposeTable);
                    // page.Content().Element(ComposeContent2);


                });
        }
        void ComposeHeader(IContainer container)
        {
            container.Column(column =>
            {
                var stream = Assembly.GetExecutingAssembly().GetManifestResourceStream("Factory.LicenseCertificatePlugin.Logo.png");
                if (stream != null)
                {

                    column.Item().Row(row =>
                    {
                        row.RelativeItem(4);
                        row.RelativeItem(1)
                            .AlignCenter()
                            .Height(50)
                            .Image(new BinaryReader(stream).ReadBytes((int)stream.Length), ImageScaling.Resize);
                        row.RelativeItem(4);
                    });
                }
                column.Item()
                    .AlignCenter()
                    .Text(text =>
                    {

                        text.Span("पुणे महानगरपालिका-परवाना विभाग").FontSize(10)
                            .FontFamily("Mangal")
                            .FontSize(10).Bold();
                    });
                column.Item()
             .AlignCenter()
             .Text(text =>
             {

                 text.Span($"(फी परत दिले जाणार नाही)               परवाना क्रमांक. ").FontSize(8)
                     .FontFamily("Mangal")
                     .FontSize(8).Bold();
                 text.Span($"{_model.LicenseNumber}").FontSize(8)
               .FontFamily("Times New Roman")
               .FontSize(8).Bold();
             });
                column.Item()
             .AlignCenter()
             .Text(text =>
             {

                 text.Span("आकाश-चिन्हाबाबतचा परवाना").FontSize(10)
                     .FontFamily("Mangal")
                     .FontSize(10).Bold();
             });
            });
        }
        void ComposeContent(IContainer container)
        {
            container.Column(column =>
            {
                column.Item()
                  .Text(text =>
                  {
                      text.Span($"श्री.")
                          .FontFamily("Mangal")
                          .FontSize(8).Bold();
                      text.Span($"{_model.Name}  ")
                 .FontFamily("Times New Roman")
                 .FontSize(8).Bold();
                      text.Span($"राहणार, पुणे, पेठ ")
                         .FontFamily("Mangal")
                         .FontSize(8).Bold();
                      text.Span($"{_model.Peth}")
                .FontFamily("Times New Roman")
                .FontSize(8).Bold();
                      text.Span($"घरांक पुणे, यांना दि. ")
                           .FontFamily("Mangal")
                           .FontSize(8).Bold();
                      text.Span($"{_model.FromDate}")
                              .FontFamily("Times New Roman")
                              .FontSize(8).Bold();
                      text.Span($"ते  दि. ")
            .FontFamily("Mangal")
            .FontSize(8).Bold();
                      text.Span($"{_model.ToDate} ")
                            .FontFamily("Times New Roman")
                            .FontSize(8).Bold();
                      text.Span($" अखेर पर्यंत खाली नमूद केलेल्या आकाश-चिन्हांकरिता त्यासमोर लिहिलेल्या ठिकाणी खाली अटींवर परवाना देण्यात येत आहे.")
                             .FontFamily("Mangal")
                             .FontSize(8).Bold();
                  });
                // Adding the table with custom styling
                column.Item()
              .MinimalBox()
              .Border(1)
                      .Table(table =>
                      {
                          IContainer DefaultCellStyle(IContainer cellContainer, string backgroundColor)
                          {
                              return cellContainer
                                  .Border(1)
                                  .BorderColor(Colors.Grey.Lighten1)
                                  .Background(backgroundColor)
                                  .PaddingVertical(5)
                                  .PaddingHorizontal(10)
                                  .AlignCenter()
                                  .AlignMiddle();
                          }

                          table.ColumnsDefinition(columns =>
                          {
                              columns.RelativeColumn();
                              columns.ConstantColumn(75);
                              columns.ConstantColumn(75);
                              columns.ConstantColumn(75);
                              columns.ConstantColumn(75);
                              columns.RelativeColumn();
                          });

                          table.Header(header =>
                          {
                              header.Cell().RowSpan(2).Element(CellStyle).Text("आकाश चिन्हाचा प्रकार").FontFamily("Mangal")
                                  .FontSize(8).Bold();
                              header.Cell().RowSpan(2).Element(CellStyle).Text("आकाशचिन्ह लावण्याची ठिकाण").FontFamily("Mangal")
                                  .FontSize(8).Bold();
                              header.Cell().RowSpan(2).Element(CellStyle).Text("आकाश चिन्हाचे क्षेत्रफळ").FontFamily("Mangal")
                                  .FontSize(8).Bold();
                              header.Cell().ColumnSpan(2).Element(CellStyle).Text("मुदतवाढ").FontFamily("Mangal")
                                  .FontSize(8).Bold();
                              header.Cell().RowSpan(2).Element(CellStyle).Text("अधिकाऱ्यांची स्वाक्षरी").FontFamily("Mangal")
                                  .FontSize(8).Bold();
                              header.Cell().Element(CellStyle).Text("पासून").FontFamily("Mangal")
                                  .FontSize(8).Bold();
                              header.Cell().Element(CellStyle).Text("पर्यंत").FontFamily("Mangal")
                                  .FontSize(8).Bold();

                              // you can extend existing styles by creating additional methods
                              IContainer CellStyle(IContainer cellContainer) => DefaultCellStyle(cellContainer, Colors.Grey.Lighten3);
                          });

                          table.Cell().Element(CellStyle).Text(_model.TypeofHoading).FontSize(8);
                          table.Cell().Element(CellStyle).Text(_model.Place).FontSize(8);
                          table.Cell().Element(CellStyle).Text(_model.Dimention).FontSize(8);
                          table.Cell().Element(CellStyle).Text(_model.From).FontSize(8);
                          table.Cell().Element(CellStyle).Text(_model.To).FontSize(8);
                          table.Cell().Element(CellStyle).Text(_model.Signature).FontSize(8);

                          IContainer CellStyle(IContainer cellContainer) => DefaultCellStyle(cellContainer, Colors.White).ShowOnce();
                      });
                column.Item()
               .Text(text =>
               {

                   text.Span($"मा.महापालिका आयुक्त यांनी पुणे महानगरपालिकेच्या संमतीने कलम, ३८६ अन्वये ठरविल्याप्रमाणे या परवान्याची फी रक्कम रु.").FontSize(8)
                       .FontFamily("Mangal")
                       .FontSize(8).Bold();
                   text.Span($" {_model.Fee}").FontSize(8)
                  .FontFamily("Times New Roman")
                  .FontSize(8).Bold();
                   text.Span($" पैसे फक्त दि. ").FontSize(8)
                .FontFamily("Mangal")
                .FontSize(8).Bold();
                   text.Span($"{_model.CurrentDate}").FontSize(8)
                    .FontFamily("Times New Roman")
                    .FontSize(8).Bold();
                   text.Span($" रोजी चलन क्र.").FontSize(8)
                 .FontFamily("Mangal")
                 .FontSize(8).Bold();
                   text.Span($"{_model.Number1}").FontSize(8)
                                   .FontFamily("Times New Roman")
                                   .FontSize(8).Bold();
                   text.Span($" ने महानगरपालिकेच्या कोशकार्यालयात भरणा केली आहे .").FontSize(8)
                                                    .FontFamily("Mangal")
                                                    .FontSize(8).Bold();
               });

                column.Item().PaddingTop(20).Row(row =>
                {
                    row.RelativeItem(1)
                            .AlignCenter().Text(text =>
                            {
                                text.Span($"दि. ")
                                .FontFamily("Mangal")
                            .FontSize(8);
                                text.Span($"{_model.CurrentDate} ")
                                  .FontFamily("Times New Roman")
                              .FontSize(8);
                            });
                    row.RelativeItem(1)
                            .AlignCenter().Text(text =>
                            {
                                text.Span("परवाना व आकाशचिन्ह निरीक्षक")
                                .FontFamily("Mangal")
                            .FontSize(8);
                            });
                    row.RelativeItem(1)
                  .AlignRight().Text(text =>
                  {
                      text.Span(" प्रमुख परवाना निरीक्षक ,")
                      .FontFamily("Mangal")
                  .FontSize(8);
                  });
                });
                column.Item()
              .AlignRight()
                .Text(text =>
                {
                    text.Span("पुणे महानगरपालिका").FontSize(10)
                 .FontFamily("Mangal")
                 .FontSize(8).Bold();
                });
                column.Item().PaddingVertical(5).LineHorizontal(1).LineColor(Colors.Grey.Darken1);
                column.Item()
               .AlignCenter()
                 .Text(text =>
                 {
                     text.Span("परवान्याबाबत  अटी").FontSize(9)
                  .FontFamily("Mangal")
                  .FontSize(9).Bold();
                 });
                column.Item().Text(text =>
                 {
                     text.Span(@"
            १. परवाना धरणा करणाऱ्याने आकाशचिन्ह जाहिरात फलक महानगरपालिकेने मंजूर केलेल्या ठिकाणीच उभे केले पाहिजेत तसेच ज्या कालावधीपर्यंत आकाश चिन्ह जाहिरात फलक लावण्यास परवानगी दिलेली आहे त्या मुदतीत आकाश चिन्ह तथा जाहिरात फलक सुस्थितीत ठेवण्याची संपूर्ण जबाबदारी अर्जदारावर राहील 
            २. परवानाधारकाने जाहिरात फलकावर दिसेल अशा ठिकाणी एका बाजूस परवाना क्रमांक व जाहिरातदार संस्थेचे नाव अथवा संस्थेचे चिन्ह लावणे बंधनकारक आहे
            ३. आकाशचिन्ह तथा जाहिरात फलक प्रत्यक्ष उभा करण्यापूर्वी अर्जासमवेत देण्यात आलेल्या तांत्रिक अभियंत्याची प्रमाणपत्रानुसार प्रत्यक्ष उभारणी केली असल्याचा दाखला परवानाधारकाने परवाना दिल्यापासून मनपाने दिलेल्या मुदतीत महापालिकेकडे दाखल करावा व त्यानंतरच प्रत्यक्षात जाहिरात फलक तथा आकाशचिन्ह उभे करावे आकाशचिन्ह तथा जाहिरात फलका मुळे कोणत्याही प्रकारचा धोका किंवा नुकसान होणार नाही याची दक्षता संबंधित परवानाधारकाने घ्यावी अशा प्रकारच्या अपघातास अथवा नुकसानिस मनपा जबाबदार राहणार नाही.
            ४. ज्या आकाशचिन्हास तथा जाहिरात फलकास परवाना दिलेला आहे अशा आकाश चिन्हास
            (१) मनपा पूर्व मान्यते शिवाय ते बदल केला असेल
            (२) अपघातामुळे अथवा इतर कोणत्याही नैसर्गिक अथवा अनैसर्गिक कारणांमुळे आकाश चिन्हाचा तथा जाहिरात फलकाचा अथवा त्याचा कोणताही भाग पडेल.
            (३) ज्या इमारतीवर आकाशचिन्ह तथा जाहिरात फलक उभे करण्यात आले आहेत त्या इमारतीबाबत कोणतीही कायदेशीर कारवाई करण्याचा निर्णय मनपाने घेतला असेल.
            (४) चार संबंधित इमारतीच्या मालकाने इमारतीच्या बांधकामांमध्ये फेरबदल करण्याचा प्रस्ताव मनपाकडे सादर केला असेल त्यास मनपाने परवानगी दिली असेल.
                अशावेळी परवाना धारकास देण्यात आलेला परवाना रद्द समजण्यात येईल.
            ५.  सार्वजनिक उपक्रमासाठी विशेषत रस्ता रुंदीमुळे एखादा जाहिरात फलक बाधित होत असेल तर त्याबाबत मनपाणे संबंधित जाहिरात धारकास तशी लेखी समज दिली असेल तर मनपा ने दिलेल्या मुदतीच्या आत परवानाधारकास स्वखर्चाने जाहिरात फलक(त्यासाठी केलेल्या बांधकामासह) काढून घ्यावा लागेल.अन्यथा मनपा प्रशासनातर्फे फलक व त्यासाठी केलेले बांधकाम काढून टाकण्यात येईल व त्यासाठी येणारा खर्च संबंधितांकडून एकरकमी वसूल केला जाईल.
            ६. क्रमांक ४ व ५ मुळे जाहिरात फलक बाधित असल्यास त्यासाठी कोणतीही पर्यायी व्यवस्था करण्याची जबाबदारी मनपावर राहणार नाही.
            ७. हा परवाना, परवाना दिनांक पासून अमलात येईल.परवानाधारकाने परवाना दिल्या तारखेपासून  मनपा ने दिलेल्या मुदतीच्या आत सहा महिन्यांच्या भाड्या इतकी रक्कमअनामत म्हणून भरावी लागेल तसेच वार्षिक फी ची रक्कम प्रत्येक सहामाहीसाठी आगाऊ भरावी लागेल.फी आकारणीसाठी सहामाही खालील प्रमाणे राहील.
            (१) १ एप्रिल ते ३० सप्टेंबर
            (२) १ ऑक्टोंबर ते ३१ मार्च
            ८. परवानाधारकास विहीत मुदतीनंतर जाहिरात फलक पुढे चालू ठेवायचा असल्यास विहीत मुदत संपण्यापूर्वी किमान 30 दिवस अगोदर मुदत वाढीसाठी रीतसर अर्ज करावा.मात्र मुदतवाढ देण्याचे कोणतेही बंधन मनपा प्रशासनावर राहणार नाही.अशा प्रकारे मुदत वाढ न दिल्यास लावण्यात आलेल्या आकाश चिन्ह वा जाहिरात फलक काढून घेण्याची जबाबदारी जाहिरातदारावर राहील.
            ९. देण्यात आलेला परवाना फक्त परवानाधारकालाच उपयोगात आणता येईल इतर कोणतीही व्यक्ती अगर संस्था यांना मनपाच्या पूर्व मान्यते शिवाय त्याचा वापर करता येणार नाही
            १०. हा परवाना दिनांक …………..पर्यंत मात्र पात्र राहील.")
                          .FontFamily("Mangal")
                       .FontSize(7);
                 });
                column.Item().Row(row =>
      {
          row.RelativeItem(1)
                .AlignRight().Text(text =>
                {
                    text.Span("माननीय उप-आयुक्त ( विशेष )")
                    .FontFamily("Mangal")
                .FontSize(9);
                });
      });

            });
        }
    }

    public static class Mapper
    {
        public static T Map<T>(ExpandoObject source) where T : class
        {
            T destination = (T)Activator.CreateInstance(typeof(T));
            IDictionary<string, object> dict = source;
            var type = destination.GetType();

            foreach (var prop in type.GetProperties())
            {
                var lower = prop.Name.ToLower();
                Console.WriteLine($"Mapping {lower}");
                var key = dict.Keys.SingleOrDefault(k => k.ToLower() == lower);

                if (key != null)
                {
                    prop.SetValue(destination, CastProperty(dict[key], prop.PropertyType.ToString()), null);
                }
            }
            return destination;
        }

        static dynamic CastProperty(dynamic property, string type)
        {
            switch (type)
            {
                case "System.String":
                    return (String)property;
                case "System.Decimal":
                    return (Decimal)property;
                default:
                    return property;

            }
        }
        public class Model
        {
            public string LicenseNumber { get; set; }
            public string CurrentDate { get; set; }
            public string Fee { get; set; }
            public string Peth { get; set; }
            public string Name { get; set; }
            public string FromDate { get; set; }
            public string ToDate { get; set; }
            public string Number1 { get; set; }
            public string Number2 { get; set; }
            public string Text { get; set; }
            public string TypeofHoading { get; set; }
            public string Place { get; set; }
            public string Dimention { get; set; }
            public string From { get; set; }
            public string To { get; set; }
            public string Signature { get; set; }
        }


    }
}
