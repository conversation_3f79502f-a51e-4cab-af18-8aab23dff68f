using QuestPDF.Fluent;
using QuestPDF.Infrastructure;
using QuestPDF.Drawing;
using Svg.Skia;
using Factory.Plugins;
using Factory.Models;
using System.Reflection;
using System.Dynamic;
using QuestPDF.Helpers;

namespace Factory.SECertificatePlugin
{
    public class SECertificatePlugin : IRemoteCallPlugin
    {
        public string GetName()
        {
            return "SE PDF";
        }

        public List<string> GetMethods()
        {
            return new List<string>() {
                "Generate"
            };
        }

        public string GetHelp()
        {
            return "";
        }

        public List<FieldMeta> GetConfigurationMeta()
        {
            return new List<FieldMeta>() {
                new FieldMeta() {
                    Name = "WaterMark",
                    Category = FieldCategory.Primitive,
                    IsCollection = false,
                    IsRequired = true,
                    IsUnique = false,
                    StorageType = StorageType.Plain,
                    Type = PrimitiveType.File.ToString()
                },
                 new FieldMeta() {
                    Name = "Logo",
                    Category = FieldCategory.Primitive,
                    IsCollection = false,
                    IsRequired = true,
                    IsUnique = false,
                    StorageType = StorageType.Plain,
                    Type = PrimitiveType.File.ToString()
                }
            };
        }

        private byte[] _waterMark { get; set; }
        private byte[] _logo { get; set; }
        private List<KeyValuePair<string, string>> _headers { get; set; }

        public void Configure(dynamic configuration)
        {
            var configDict = (IDictionary<string, object>)configuration;
            _waterMark = (byte[])configDict["WaterMark"];
            _logo = (byte[])configDict["Logo"];
            // _waterMark = ((IDictionary<string, byte[]>)configuration)["WaterMark"];
            // _logo = ((IDictionary<string, byte[]>)configuration)["Logo"];
        }

        public Task<dynamic> Invoke(dynamic input)
        {
            dynamic output = new ExpandoObject();
            input.Logo = _logo;
            input.WaterMark = _waterMark;
            output.Result = new SECertificate(Mapper.Map<Model>(input)).GeneratePdf();
            return Task.FromResult<dynamic>(output);
        }
    }

    public class SECertificate : IDocument
    {
        static SECertificate()
        {
            var mangalFont = Assembly.GetExecutingAssembly().GetManifestResourceStream("Factory.SECertificatePlugin.Mangal.ttf");
            if (mangalFont != null)
            {
                FontManager.RegisterFont(mangalFont);
            }
            var timesFont = Assembly.GetExecutingAssembly().GetManifestResourceStream("Factory.SECertificatePlugin.times.ttf");
            if (timesFont != null)
            {
                FontManager.RegisterFont(timesFont);
            }
        }

        public Model _model { get; }

        public SECertificate(Model model)
        {
            _model = model;
        }

        public DocumentMetadata GetMetadata() => DocumentMetadata.Default;

        public void Compose(IDocumentContainer container)
        {
            container
                .Page(page =>
                {
                    page.Margin(30);
                    page.Header().Element(ComposeHeader);
                    page.Content().Element(ComposeContent);

                });
        }

        void ComposeHeader(IContainer container)
        {
            container.Column(column =>
            {
                column.Item().Row(row =>
        {
            row.RelativeItem(4);

            if (_model.Logo != null) // Add this null check
            {
                row.RelativeItem(1)
                    .AlignCenter()
                    .Height(50)
                    .Image(_model.Logo, ImageScaling.Resize);
            }

            row.RelativeItem(4);
        });


                column.Item()
                    .AlignCenter()
                    .Text(text =>
                    {
                        text.Span("पुणे महानगरपालिका").FontSize(10)
                            .FontFamily("Mangal")
                            .FontSize(10).Bold();
                    });
                column.Item()
                    .AlignCenter()
                    .Text(text =>
                    {
                        text.Span("स्ट्रक्चरल इंजिनिअरच्या कामासाठी परवाना")
                            .FontFamily("Mangal")
                            .FontSize(10).Bold();
                    });
            });

        }

        void ComposeContent(IContainer container)
        {
            container.Column(column =>
            {
                column.Item().Text(text =>
                {
                    text.Span("महाराष्ट्र प्रादेशिक अधिनियम १९६६ चे कलम ३७ (१ कक )(ग) कलम २० (४)/ नवि-१३ दि.२/१२/२०२० अन्वये पुणे शहरासाठी मान्य झालेल्या एकत्रिकृत विकास नियंत्रण व प्रोत्साहन नियमावली (युडीसीपीआर -२०२०)  नियम क्र.अपेंडिक्स ‘सी’ अन्वये महाराष्ट्र महानगरपालिका अधिनियम १९४९ चे कलम ३७२ अन्वये स्ट्रक्चरल इंजिनिअर काम करण्यास परवाना देण्यात येत आहे.")
                        .FontFamily("Mangal")
                        .FontSize(10);
                });
                column.Item().Text(text =>
                {
                    text.Span($"परवाना क्र. ")
                        .FontFamily("Mangal")
                        .FontSize(10);
                    text.Span($"{_model.CertificateNumber} From {_model.FromDate: dd/MM/yyyy} to 31/12/{_model.ToYear}")
                        .FontFamily("Times New Roman")
                        .FontSize(10);
                });
                column.Item().Text(text =>
                {
                    text.Span($"नाव :- ")
                        .FontFamily("Mangal")
                        .FontSize(10);
                    text.Span(_model.Name)
                        .FontFamily("Times New Roman")
                        .FontSize(10);
                });
                column.Item().Text(text =>
                {
                    text.Span($"पत्ता :- ")
                        .FontFamily("Mangal")
                        .FontSize(10);
                    text.Span(_model.Address)
                        .FontFamily("Times New Roman")
                        .FontSize(10);
                });
                column.Item().Text(text =>
                {
                    text.Span($"महाराष्ट्र प्रादेशिक अधिनियम १९६६ चे कलम ३७ (१ कक )(ग) कलम २० (४)/ नवि-१३ दि.२/१२/२०२०  अन्वये पुणे शहरासाठी मान्य झालेल्या एकत्रिकृत विकास नियंत्रण व प्रोत्साहन नियमावली (युडीसीपीआर -२०२०)  नियम क्र.अपेंडिक्स ‘सी’ अन्वये महाराष्ट्र महानगरपालिका अधिनियम १९४९ चे कलम ३७२ अन्वये मी तुम्हास वर निर्देश केलेल्या कायदा व नियमानुसार  ३ वर्षांकरीता  दि. {_model.FromDate:dd/MM/yyyy} ते 31/12/{_model.ToYear} अखेर स्ट्रक्चरल इंजिनिअर म्हणून खालील मर्यादा व अटी यांचे पालन करणार या अटीवर परवाना देत आहे.")
                        .FontFamily("Mangal")
                        .FontSize(10);
                });
                column.Item().Text(text =>
                {
                    text.Span($"मा. महापालिका आयुक्त यांनी वेळोवेळी स्थायी समितीच्या संमतीने वरील कायद्याचे कलम ३७३ परवानाधारण करणार  यांच्या माहितीसाठी काढण्यात आलेल्या आज्ञेचे आणि एकत्रिकृत विकास नियंत्रण व प्रोत्साहन नियमावलीतील अपेंडिक्स ‘सी’ मधील कर्तव्ये व जबाबदारी यांचे पालन करणार’ ही परवानगीची अट राहील आणि धंधाच्या प्रत्येक बाबतीत परवान्याच्या मुदतीत ज्यावेळी तुमचा सल्ला घेण्यात येईल त्यावेळी तुम्ही आतापावेतो निघालेल्या आज्ञांचे पालन करून त्याप्रमाणे काम करावयाचे आहे.")
                        .FontFamily("Mangal")
                        .FontSize(10);
                });
                column.Item().Text(text =>
                {
                    text.Span($"जी आज्ञापत्रक वेळोवेळी काढण्यात आलेली आहेत, ती मुख्य कार्यालयाकडे माहितीसाठी ठेवण्यात आलेली असून, जरूर त्यावेळी कार्यालयाच्या वेळेमध्ये त्यांची पाहणी करता येईल.")
                        .FontFamily("Mangal")
                        .FontSize(10);
                });
                column.Item().Text(text =>
                {
                    text.Span($"मात्र हे लक्षात घेणे जरूर आहे की, मा. महापालिका आयुक्त सदरचा परवाना महाराष्ट्र महानगरपालिका अधिनियम, कलम ३८६ अनुसार जरूर तेव्हा तात्पुरता बंद अगर रद्द करू शकतात जर वर निर्दिष्ठ केलेली बंधने अगर शर्थी यांचा भंग झाला अगर टाळल्या गेल्या अथवा तुम्ही सदर कायद्याच्या नियमांचे अगर वेळोवेळी काढण्यात आलेल्या आज्ञापत्रकाचे उल्लंघन केल्याचे दृष्टीक्षेपात आल्यास आणि जर सदरचा परवाना तात्पुरता तहकूब अगर रद्द झाल्यास अथवा सदरच्या परवान्याची मुदत संपल्यावर तुम्हास परवाना नसल्याचे समजले जाईल आणि महानगरपालिका अधिनियमाचे कलम ६९ अन्वये मा. महापालिका आयुक्त अगर त्यांनी अधिकार दिलेल्या अधिका-यांनी सदर परवान्याची मागणी केल्यास सदरचा परवाना तुम्हास त्या त्या वेळी हजर करावा लागेल.")
                         .FontFamily("Mangal")
                        .FontSize(10);
                });
                column.Item().PaddingTop(50)
                        .Row(row =>
                        {
                            row.RelativeItem(1)
                                .AlignCenter().Text(text =>
                                {
                                    text.Span("कनिष्ठ अभियंता")
                                        .FontFamily("Mangal")
                                    .FontSize(10);
                                });
                            row.RelativeItem(1)
                                .AlignCenter().Text(text =>
                                {
                                    text.Span("उप अभियंता")
                                        .FontFamily("Mangal")
                                    .FontSize(10);
                                });
                            row.RelativeItem(1)
                                .AlignCenter().Text(text =>
                                {
                                    text.Span("कार्यकारी अभियंता,")
                                        .FontFamily("Mangal")
                                    .FontSize(10);
                                });
                            row.RelativeItem(1)
                                .AlignCenter().Text(text =>
                                {
                                    text.Span("शहर अभियंता")
                                        .FontFamily("Mangal")
                                    .FontSize(10);
                                });
                        });
                column.Item().Row(row =>
                {
                    row.RelativeItem(2);
                    row.RelativeItem(1)
                        .AlignCenter().Text(text =>
                        {
                            text.Span("(बांधकाम विकास विभाग)")
                                .FontFamily("Mangal")
                            .FontSize(10);
                        });
                    row.RelativeItem(1);
                });
                column.Item().Row(row =>
                {
                    row.RelativeItem(1)
                        .AlignCenter().Text(text =>
                        {
                            text.Span("पुणे महानगरपालिका")
                                .FontFamily("Mangal")
                            .FontSize(10);
                        });
                    row.RelativeItem(1)
                        .AlignCenter().Text(text =>
                        {
                            text.Span("पुणे महानगरपालिका")
                                .FontFamily("Mangal")
                            .FontSize(10);
                        });
                    row.RelativeItem(1)
                        .AlignCenter().Text(text =>
                        {
                            text.Span("पुणे महानगरपालिका")
                                .FontFamily("Mangal")
                            .FontSize(10);
                        });
                    row.RelativeItem(1)
                        .AlignCenter().Text(text =>
                        {
                            text.Span("पुणे महानगरपालिका")
                                .FontFamily("Mangal")
                            .FontSize(10);
                        });
                });
                column.Item().Text(text =>
                {
                    text.Span($"टीप – प्रस्तुत परवान्याची मुदत ३१ डिसेंबर रोजी संपते जर पुढील वर्षासाठी त्याचे नूतनीकरण करणे असेल तर यासाठी कमीत कमी १५ दिवस परवाना मुदत संपण्या अगोदर परवाना शुल्कासहित अर्ज सादर केला पाहिजे. परवान्याचे नूतनीकरण करून घेण्याबद्दल तुम्हास वेगळी समज दिली जाणार नाही जोपर्यंत परवान्याच्या नूतनीकरणासाठी परवाना शुल्कासहित अर्ज दिलेला नाही तोपर्यंत स्ट्रक्चरल इंजिनिअर म्हणून काम करता येणार नाही तसेच परवाना नाकारल्यासही तुम्हास स्ट्रक्चरल इंजिनिअर म्हणून काम करता येणार नाही.")
                        .FontFamily("Mangal")
                        .FontSize(10);
                });
                // column.Item().Row(row =>
                // {
                //     row.RelativeItem(3);
                //     row.RelativeItem(1)
                //         .AlignLeft().Text(text =>
                //         {
                //             text.Span("बांधकाम विकास विभाग")
                //                 .FontFamily("Mangal")
                //             .FontSize(10);
                //         });
                // });
                // column.Item().Row(row =>
                // {
                //     row.RelativeItem(3);
                //     row.RelativeItem(1)
                //         .AlignLeft().Text(text =>
                //         {
                //             text.Span("पुणे महानगरपालिका")
                //                 .FontFamily("Mangal")
                //             .FontSize(10);
                //         });
                // });

            });
        }
    }

    public static class Mapper
    {
        public static T Map<T>(ExpandoObject source) where T : class
        {
            T destination = (T)Activator.CreateInstance(typeof(T));
            IDictionary<string, object> dict = source;
            var type = destination.GetType();

            foreach (var prop in type.GetProperties())
            {
                var lower = prop.Name.ToLower();
                Console.WriteLine($"Mapping {lower}");
                var key = dict.Keys.SingleOrDefault(k => k.ToLower() == lower);

                if (key != null)
                {
                    prop.SetValue(destination, CastProperty(dict[key], prop.PropertyType.ToString()), null);
                }
            }
            return destination;
        }

        static dynamic CastProperty(dynamic property, string type)
        {
            switch (type)
            {
                case "System.String":
                    return (String)property;
                case "System.Decimal":
                    return (Decimal)property;
                default:
                    return property;

            }
        }

    }


    public class Model
    {
        public string CertificateNumber { get; set; }

        public byte[] Watermark { get; set; }
        
        public byte[] Logo { get; set; }
        public string Name { get; set; }
        public string Address { get; set; }
        public DateTime FromDate { get; set; }
        public int ToYear { get; set; }
    }
}
