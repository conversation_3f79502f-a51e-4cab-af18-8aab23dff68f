using QuestPDF.Fluent;
using QuestPDF.Infrastructure;
using QuestPDF.Drawing;
using Svg.Skia;
using Factory.Plugins;
using Factory.Models;
using System.Reflection;
using System.Dynamic;
using QuestPDF.Helpers;
using SkiaSharp;
using static Factory.BankReceiptCredAiPlugin.Mapper;

namespace Factory.BankReceiptCredAiPlugin
{
    public class BankReceiptCredAiPlugin : IRemoteCallPlugin
    {
        public string GetName()
        {
            return "BankReceiptCredAi";
        }

        public List<string> GetMethods()
        {
            return new List<string>() {
                "Generate"
            };
        }

        public string GetHelp()
        {
            return "";
        }

        public List<FieldMeta> GetConfigurationMeta()
        {
            return new List<FieldMeta>() { };
        }



        public void Configure(dynamic configuration)
        {
        }

        public Task<dynamic> Invoke(dynamic input)
        {

            dynamic output = new ExpandoObject();
            output.Result = new BankReceiptCredAi(Mapper.Map<Model>(input)).GeneratePdf();
            return Task.FromResult<dynamic>(output);
        }
    }

    public class BankReceiptCredAi : IDocument
    {
        static BankReceiptCredAi()
        {
            var mangalFont = Assembly.GetExecutingAssembly().GetManifestResourceStream("Factory.BankReceiptCredAiPlugin.Mangal.ttf");
            if (mangalFont != null)
            {
                FontManager.RegisterFont(mangalFont);
            }
            var timesFont = Assembly.GetExecutingAssembly().GetManifestResourceStream("Factory.BankReceiptCredAiPlugin.LiberationSans-Bold.ttf");
            if (timesFont != null)
            {
                FontManager.RegisterFont(timesFont);
            }
        }



        public Model _model { get; }

        public BankReceiptCredAi(Model model)
        {
            _model = model;
        }


        public DocumentMetadata GetMetadata() => DocumentMetadata.Default;

        public void Compose(IDocumentContainer container)
        {
            container
                .Page(page =>
                {
                    page.Margin(25);
                    page.Size(new PageSize(11, 7, Unit.Inch));
                    page.Header().Element(ComposeHeader);
                    page.Content().Element(ComposeContent);
                    // page.Content().Element(ComposeTable);
                    // page.Content().Element(ComposeContent2);


                });
        }
        void ComposeHeader(IContainer container)
        {
            var stream = Assembly.GetExecutingAssembly().GetManifestResourceStream("Factory.BankReceiptCredAiPlugin.credaiLogo.png");
            container.Column(column =>
            {

                column.Item()
               .AlignRight()
               .AlignMiddle()
               .Height(50)
               .Width(140)
               .Image(new BinaryReader(stream).ReadBytes((int)stream.Length), ImageScaling.Resize);



                column.Item()
               .AlignCenter()
               .PaddingLeft(20)
               .PaddingRight(20)
               .Text(text =>
                {
                    text.Span($"CREDAI - PUNE").FontSize(20)
                 .FontFamily("Liberation Sans")
                  .FontSize(20).Bold();
                });

                column.Item()
              .AlignCenter()
              .PaddingLeft(20)
              .PaddingRight(20)
              .Text(text =>
               {

                   text.Span($@"Office Address:Office No.T-1,3rd Floor, Nucleus Mall Church Road, Camp, Pune 411001 
Tel.Nos: 020-********,******** Email Id:<EMAIL> Website:www.credaipune.org").FontSize(11).FontFamily("Liberation Sans");


               });

                column.Item().PaddingVertical(2).LineHorizontal(1).LineColor(Colors.Grey.Darken1);

                column.Item().PaddingLeft(20).PaddingRight(50).Row(row =>
                {
                    row.ConstantItem(250)
                            .AlignLeft().Text(text =>
                            {
                                text.Line($"RECEIVED WITH THANKS FROM")
                                .FontFamily("Liberation Sans")
                                .FontSize(11).Bold();
                                text.Line(@$"M/s {_model.CompanyName} ").FontSize(11).FontFamily("Liberation Sans");
                            });
                    row.ConstantItem(180)
                            .AlignLeft().Text(text =>
                            {
                                text.Span($"BANK RECEIPT")
                                .FontFamily("Liberation Sans")
                                .FontColor(Colors.White)
                                .BackgroundColor(Colors.Black)
                                .FontSize(14).Bold();
                            });

                    row.RelativeItem().PaddingHorizontal(3).LineVertical(1).LineColor(Colors.Grey.Darken1);

                    row.ConstantItem(155).Text(text =>
                            {
                                text.Span($"R.No. {_model.ReceiptNo}{Environment.NewLine}{Environment.NewLine}")
                                .FontFamily("Liberation Sans")
                                .FontSize(11);
                                text.Span($"Date:- {_model.Date}")
                                .FontFamily("Liberation Sans")
                                .FontSize(11);
                            });


                });








            });
        }
        void ComposeContent(IContainer container)
        {
            container.Column(column =>
            {


                column.Item()
              .MinimalBox()
              //   .Border(1)
              .PaddingLeft(80)
              .Table(table =>
    {
        IContainer DefaultCellStyle(IContainer container, string backgroundColor)
        {
            return container
            .Border(1)
            .BorderColor(Colors.Grey.Lighten1)
             .Background(backgroundColor)
            //  .PaddingLeft(30)
             .PaddingHorizontal(1)
             .PaddingVertical(1)
            .AlignCenter()
            .AlignMiddle();
        }

        table.ColumnsDefinition(columns =>
    {

        columns.ConstantColumn(50);
        columns.ConstantColumn(323);
        // columns.RelativeColumn();
        columns.ConstantColumn(250);
        // columns.ConstantColumn(55);


    });

        table.Header(header =>
    {
        // please be sure to call the 'header' handler!

        header.Cell().Column(1).Element(CellStyle).Text("Sr.No.").FontSize(11).FontFamily("Liberation Sans").Bold();
        header.Cell().Column(2).Element(CellStyle).AlignLeft().Text("Particulars").FontSize(11).FontFamily("Liberation Sans").Bold();
        header.Cell().Column(3).Element(CellStyle).AlignRight().Text("Rs.").FontSize(11).FontFamily("Liberation Sans").Bold();
        // header.Cell().Column(4).Element(CellStyle).Text("Ps").FontSize(11).FontFamily("Liberation Sans").Bold();

        // you can extend existing styles by creating additional methods
        IContainer CellStyle(IContainer container) => DefaultCellStyle(container, Colors.Grey.Lighten3);
    });
        table.Cell().Row(1).Column(1).Element(CellStyle).ExtendHorizontal().AlignCenter().Text($@"1.").FontSize(11).FontFamily("Liberation Sans").Bold();
        table.Cell().Row(2).Column(1).Element(CellStyle).ExtendHorizontal().AlignCenter().Text($@"2.").FontSize(11).FontFamily("Liberation Sans").Bold();
        // table.Cell().Row(3).Column(1).Element(CellStyle).ExtendHorizontal().AlignCenter().Text($@"").FontSize(11).FontFamily("Liberation Sans").Bold();
        table.Cell().Row(3).Column(1).Element(CellStyle).ExtendHorizontal().AlignCenter().Text($@"3.").FontSize(11).FontFamily("Liberation Sans").Bold();
        table.Cell().Row(4).Column(1).Element(CellStyle).ExtendHorizontal().AlignCenter().Text($@"").FontSize(11).FontFamily("Liberation Sans").Bold();
        // table.Cell().Row(5).Column(1).Element(CellStyle).ExtendHorizontal().AlignCenter().Text($@"").FontSize(11).FontFamily("Liberation Sans").Bold();
        // table.Cell().Row(6).Column(1).Element(CellStyle).ExtendHorizontal().AlignCenter().Text($@"").FontSize(11).FontFamily("Liberation Sans").Bold();

        table.Cell().Row(1).Column(2).Element(CellStyle).ExtendHorizontal().AlignLeft().Text($@"Entrance Fee").FontSize(11).FontFamily("Liberation Sans").Bold();
        table.Cell().Row(2).Column(2).Element(CellStyle).ExtendHorizontal().AlignLeft().Text(@$"{_model.SubscriptionName}").FontSize(11).FontFamily("Liberation Sans").Bold();
        // table.Cell().Row(3).Column(2).Element(CellStyle).ExtendHorizontal().AlignLeft().Text($"").FontSize(11).FontFamily("Liberation Sans").Bold();
        table.Cell().Row(3).Column(2).Element(CellStyle).ExtendHorizontal().AlignLeft().Text($"GST @ 18%").FontSize(11).FontFamily("Liberation Sans").Bold();
        // table.Cell().Row(5).Column(2).Element(CellStyle).ExtendHorizontal().AlignCenter().Text($"").FontSize(11).FontFamily("Liberation Sans").Bold();
        table.Cell().Row(4).Column(2).Element(CellStyle).ExtendHorizontal().AlignRight().Text($"Total").FontSize(11).FontFamily("Liberation Sans").Bold();
        table.Cell().Row(1).Column(3).Element(CellStyle).ExtendHorizontal().AlignRight().Text($"{_model.EntranceFee}").FontSize(11).FontFamily("Liberation Sans").Bold();
        table.Cell().Row(2).Column(3).Element(CellStyle).ExtendHorizontal().AlignRight().Text($"{_model.AnnualSub}").FontSize(11).FontFamily("Liberation Sans").Bold();
        // table.Cell().Row(3).Column(3).Element(CellStyle).ExtendHorizontal().AlignCenter().Text($"").FontSize(11).FontFamily("Liberation Sans").Bold();
        table.Cell().Row(3).Column(3).Element(CellStyle).ExtendHorizontal().AlignRight().Text($"{_model.GST}").FontSize(11).FontFamily("Liberation Sans").Bold();
        // table.Cell().Row(5).Column(3).Element(CellStyle).ExtendHorizontal().AlignCenter().Text($"").FontSize(11).FontFamily("Liberation Sans").Bold();
        table.Cell().Row(4).Column(3).Element(CellStyle).ExtendHorizontal().AlignRight().Text($"{_model.TotalAmt}").FontSize(11).FontFamily("Liberation Sans").Bold();
        // table.Cell().Row(1).Column(4).Element(CellStyle).ExtendHorizontal().AlignCenter().Text($"").FontSize(11).FontFamily("Liberation Sans").Bold();
        // table.Cell().Row(2).Column(4).Element(CellStyle).ExtendHorizontal().AlignCenter().Text($"").FontSize(11).FontFamily("Liberation Sans").Bold();
        // table.Cell().Row(3).Column(4).Element(CellStyle).ExtendHorizontal().AlignCenter().Text($"").FontSize(11).FontFamily("Liberation Sans").Bold();
        // table.Cell().Row(4).Column(4).Element(CellStyle).ExtendHorizontal().AlignCenter().Text($"").FontSize(11).FontFamily("Liberation Sans").Bold();
        // table.Cell().Row(5).Column(4).Element(CellStyle).ExtendHorizontal().AlignCenter().Text($"").FontSize(11).FontFamily("Liberation Sans").Bold();
        // table.Cell().Row(6).Column(4).Element(CellStyle).ExtendHorizontal().AlignCenter().Text($"").FontSize(11).FontFamily("Liberation Sans").Bold();
        // table.Cell().Row(5).Column(4).Element(CellStyle).ExtendHorizontal().AlignCenter().Text($"").FontSize(11).FontFamily("Liberation Sans").Bold();



        IContainer CellStyle(IContainer container) => DefaultCellStyle(container, Colors.White).ShowOnce();
    });
                column.Item().PaddingLeft(10).PaddingRight(10).PaddingTop(10).Row(row =>
                  {
                      row.ConstantItem(200)
                              .AlignLeft().Text(text =>
                              {

                                  text.Span($"Transaction ID: {_model.TrnId}").FontSize(12).FontFamily("Liberation Sans");
                              });

                      row.ConstantItem(140)
                             .AlignLeft().Text(text =>
                             {
                                 text.Span($"   Date: {_model.Date}")
                                 .FontFamily("Liberation Sans")
                                 .FontSize(12);
                             });

                      row.ConstantItem(110).Text(text =>
                              {
                                  text.Span($"Amount in words:")
                                  .FontFamily("Liberation Sans")
                                  .FontSize(12);
                              });

                      row.ConstantItem(260)
                              .AlignLeft().Text(text =>
                              {
                                  text.Span($"{ConvertNumberToWords(_model.AmtInWords)} Only")
                                              .FontFamily("Liberation Sans")
                                              .FontSize(12);
                              });




                  });


                //     column.Item()
                //    .AlignLeft()
                //    .PaddingLeft(10)
                //    .PaddingRight(10)
                //    .Text(text =>
                //     {

                //         text.Span($"Bank: {_model.BankDetails}").FontSize(12).FontFamily("Liberation Sans");


                //     });

                column.Item().PaddingHorizontal(3).PaddingTop(10).LineHorizontal(1).LineColor(Colors.Grey.Darken1);



                column.Item().PaddingTop(10).Row(row =>
                       {
                           row.RelativeItem(1).AlignCenter()
                            .Text(text =>
                            {

                                text.Line($"GSTIN No.: 27**********1Z6").FontSize(11).FontFamily("Liberation Sans").SemiBold();
                                text.Line($"PAN No: **********").FontSize(11).FontFamily("Liberation Sans").SemiBold();
                            });
                           row.RelativeItem(1)
                                  .AlignCenter()
                                  .AlignMiddle()
                                  .Text(text =>
                                  {
                                      text.Span($"FOR CREDAI-Pune{Environment.NewLine}").FontSize(11).FontFamily("Liberation Sans").SemiBold();
                                      text.Span($"Sd/-{Environment.NewLine}").FontSize(11).FontFamily("Liberation Sans");
                                      text.Span($"SECRETARY/TREASURER").FontSize(11).FontFamily("Liberation Sans").SemiBold();
                                  });

                       });

                // column.Item().PaddingBottom(100).AlignRight().Text(text =>
                //                       {
                //                           text.Line($"FOR CREDAI-PUNE").FontSize(11).FontFamily("Liberation Sans").SemiBold();
                //                           text.Line($"Sd/-").FontSize(11).FontFamily("Liberation Sans");
                //                           text.Line($"SECRETARY/TREASURER").FontSize(11).FontFamily("Liberation Sans").SemiBold();

                //                       });








            });

        }
        static string ConvertNumberToWords(string numericString)
        {
            string[] digitWords = { "Zero", "One", "Two", "Three", "Four", "Five", "Six", "Seven", "Eight", "Nine" };
            string[] tensWords = { "Ten", "Twenty", "Thirty", "Forty", "Fifty", "Sixty", "Seventy", "Eighty", "Ninety" };
            string[] teensWords = { "Eleven", "Twelve", "Thirteen", "Fourteen", "Fifteen", "Sixteen", "Seventeen", "Eighteen", "Nineteen" };

            if (!string.IsNullOrWhiteSpace(numericString))
            {
                if (numericString == "0")
                {
                    return "Zero";
                }

                int number;
                if (int.TryParse(numericString, out number))
                {
                    if (number < 0 || number > 999999)
                    {
                        return "Invalid input";
                    }
                    if (number == 1000)
                    {
                        return "One Thousand";
                    }

                    string result = "";

                    int lakhs = number / 100000;
                    int thousands = (number % 100000) / 1000;
                    int hundreds = (number % 1000) / 100;
                    int tens = (number % 100) / 10;
                    int ones = number % 10;

                    if (lakhs > 0)
                    {
                        result += ConvertNumberToWords(lakhs.ToString()) + " Lakh";
                    }

                    if (thousands > 0)
                    {
                        if (result != "")
                        {
                            result += " ";
                        }
                        result += ConvertNumberToWords(thousands.ToString()) + " Thousand";
                    }

                    if (hundreds > 0)
                    {
                        if (result != "")
                        {
                            result += " ";
                        }
                        result += digitWords[hundreds] + " Hundred";
                    }

                    if (tens > 1)
                    {
                        if (result != "")
                        {
                            result += " ";
                        }
                        result += tensWords[tens - 1];
                        if (ones > 0)
                        {
                            result += "-" + digitWords[ones];
                        }
                    }
                    else if (tens == 1)
                    {
                        if (result != "")
                        {
                            result += " ";
                        }
                        result += teensWords[ones - 1];
                    }
                    else if (ones > 0)
                    {
                        if (result != "")
                        {
                            result += " ";
                        }
                        result += digitWords[ones];
                    }

                    return result;
                }
            }

            return "Invalid input";
        }
    }

    public static class Mapper
    {
        public static T Map<T>(ExpandoObject source) where T : class
        {
            T destination = (T)Activator.CreateInstance(typeof(T));
            IDictionary<string, object> dict = source;
            var type = destination.GetType();

            foreach (var prop in type.GetProperties())
            {
                var lower = prop.Name.ToLower();
                Console.WriteLine($"Mapping {lower}");
                var key = dict.Keys.SingleOrDefault(k => k.ToLower() == lower);

                if (key != null)
                {
                    prop.SetValue(destination, CastProperty(dict[key], prop.PropertyType.ToString()), null);
                }
            }
            return destination;
        }

        static dynamic CastProperty(dynamic property, string type)
        {
            switch (type)
            {
                case "System.String":
                    return (String)property;
                case "System.Decimal":
                    return (Decimal)property;
                default:
                    return property;

            }
        }
        public class Model
        {
            public string ReceiptNo { get; set; }
            public string Date { get; set; } = DateTime.Now.ToString("dd-MM-yyyy");
            public string CompanyName { get; set; }
            public string SubscriptionName { get; set; }
            public string BankDetails { get; set; }
            public string EntranceFee { get; set; }
            public string AnnualSub { get; set; }
            public string TotalAmt { get; set; }
            public string GST { get; set; }
            public string TrnId { get; set; }
            public string ChqDate { get; set; }
            public string AmtInWords { get; set; }
        }


    }
}
