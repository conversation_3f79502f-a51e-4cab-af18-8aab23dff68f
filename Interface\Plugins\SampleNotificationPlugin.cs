using System.Collections.Generic;
using System.Net;
using System.Net.Mail;
using Factory.Plugins;
using Factory.Models;

namespace Factory.Plugins
{

    public class SampleNotificationPlugin : INotificationPlugin
    {
        public string GetName()
        {
            return "";
        }
        public List<FieldMeta> GetConfigurationMeta()
        {
            return new List<FieldMeta>()
            {
            };
        }

        public void Configure(dynamic configuration)
        {
        }

        public Task Notify(List<string> toList, string subject, string body, byte[] byteArray)
        {
            return Task.FromResult(0);
        }

        public List<string> GetMethods()
        {
            throw new NotImplementedException();
        }

        public string GetHelp()
        {
            throw new NotImplementedException();
        }
    }
}