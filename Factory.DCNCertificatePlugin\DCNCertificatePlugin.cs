using QuestPDF.Fluent;
using QuestPDF.Infrastructure;
using QuestPDF.Drawing;
using Svg.Skia;
using Factory.Plugins;
using Factory.Models;
using System.Reflection;
using System.Dynamic;

namespace Factory.DCNCertificatePlugin
{
    public class DCNCertificatePlugin : IRemoteCallPlugin
    {
        public string GetName()
        {
            return "DCN PDF";
        }

        public List<string> GetMethods()
        {
            return new List<string>() {
                "Generate"
            };
        }

        public string GetHelp()
        {
            return "";
        }

        public List<FieldMeta> GetConfigurationMeta()
        {
            return new List<FieldMeta>() { };
        }



        public void Configure(dynamic configuration)
        {
        }

        public Task<dynamic> Invoke(dynamic input)
        {
            dynamic output = new ExpandoObject();
            output.Result = new DCNDocument(Mapper.Map<Model>(input)).GeneratePdf();
            return Task.FromResult<dynamic>(output);
        }
    }

    public class DCNDocument : IDocument
    {
        static DCNDocument() {
            var calibri = Assembly.GetExecutingAssembly().GetManifestResourceStream("Factory.DCNCertificatePlugin.calibri.ttf");
            if (calibri != null) {
                FontManager.RegisterFont(calibri);
            }
            var times = Assembly.GetExecutingAssembly().GetManifestResourceStream("Factory.DCNCertificatePlugin.times.ttf");
            if (times != null) {
                FontManager.RegisterFont(times);
            }
        }

        public Model Model { get; }

        public DCNDocument(Model model)
        {
            Model = model;
        }

        public DocumentMetadata GetMetadata() => DocumentMetadata.Default;

        public void Compose(IDocumentContainer container)
        {
            container
                .Page(page =>
                {
                    var stream = Assembly.GetExecutingAssembly().GetManifestResourceStream("Factory.DCNCertificatePlugin.Background.png");
                    if (stream != null)
                    {
                        page.Background()
                            .Image(new BinaryReader(stream).ReadBytes((int)stream.Length), ImageScaling.Resize);
                    }
                    
                    page.Margin(25);

                    page.Header().Element(ComposeHeader);
                    page.Content().Element(ComposeContent);

                });
        }

        void ComposeHeader(IContainer container)
        {
            container.Column(column =>
            {
                column.Item().Row(row =>
                {
                    row.RelativeItem()
                        .PaddingRight(5)
                        .AlignRight()
                        .Text(text =>
                    {
                        text.Span($"{Model.DCNId}")
                            .FontFamily("Calibri")
                            
                            .FontSize(9.5f);
                    });
                });
                column.Item().Row(row =>
                {
                    row.Spacing(10);
                    row.RelativeItem(2);
                    row.RelativeItem(1);
                    row.RelativeItem(1);
                    row.RelativeItem(1)
                        .Height(100)
                        .Scale(2)
                        .Canvas((canvas, space) =>
                        {
                            using var svg = new SKSvg();
                            svg.FromSvg(Model.Qr);

                            canvas.DrawPicture(svg.Picture);
                        });

                });
                column.Item().Height(150);
            });

        }

        void ComposeContent(IContainer container)
        {
            container
                .Column(column =>
                {
                    column.Item().Element(ComposeContentHeader);
                });
        }

        void ComposeContentHeader(IContainer container)
        {
            container.Padding(10).Column(column =>
            {
                column.Spacing(5);
                column.Item()
                    .Text(text =>
                    {
                        text.Span("I Municipal Commissioner for PMC Pune, certify that, the person (S) /Institution / Bidder within named in this certificate is /are the register holder (S) of this DEVELOPMENT CREDIT NOTE which is issued as per Principal sanctioned by standing committee resolution no.538 dated 6/7/2021 for the development of DP road /Bridge in the village ")
                            .FontFamily("Times New Roman")
                            .FontSize(10.5f);
                        text.Span(Model.PethName)
                            .FontFamily("Times New Roman")
                            .FontSize(10.5f)
                            .Underline();
                        text.Span(" within the Pune Municipal Corporation area which was sanctioned by standing corrimittee resolution no. ")
                            .FontFamily("Times New Roman")
                            .FontSize(10.5f);
                        text.Span(Model.ResolutionNumber)
                            .FontFamily("Times New Roman")
                            .FontSize(10.5f)
                            .Underline();
                        text.Span(" dated ")
                            .FontFamily("Times New Roman")
                            .FontSize(10.5f);
                        string formattedDate = Model.IssuedDate.ToString("dd/mm/yyyy");
                        text.Span(formattedDate)
                            .FontFamily("Times New Roman")
                            .FontSize(10.5f)
                            .Underline();
                        // text.Span(" details which in below")
                        //     .FontFamily("Times New Roman")
                        //     .FontSize(10.5f);
                    });

                column.Item().PaddingTop(10).Row(row =>
                {
                    row.RelativeItem().Text(text =>
                    {
                        text.Span("1) Name of the work: ").FontFamily("Times New Roman").FontSize(10.5f);
                        text.Span(Model.TenderName).FontFamily("Times New Roman").FontSize(10.5f).Underline();
                    });
                });
                column.Item().Row(row =>
                {
                    row.Spacing(5);
                    row.RelativeItem().Text(text =>
                    {
                        text.Span("2) Name of Bidder i.e. Development Credit Note holder: ").FontFamily("Times New Roman").FontSize(10.5f);
                        text.Span(Model.VendorName).FontFamily("Times New Roman").FontSize(10.5f).Underline();
                    });
                });
                column.Item().Row(row =>
                {
                    row.Spacing(5);
                    row.RelativeItem().Text(text =>
                    {
                        text.Span("3) Work Order: ").FontFamily("Times New Roman").FontSize(10.5f);
                        text.Span(Model.WorkOrderNumber).FontFamily("Times New Roman").FontSize(10.5f).Underline();
                    });
                });
                column.Item().Row(row =>
                {
                    row.Spacing(5);
                    row.RelativeItem().Text(text =>
                    {
                        text.Span("4) Tender Amount: ").FontFamily("Times New Roman").FontSize(10.5f);
                        text.Span(Model.TenderAmount.ToString()).FontFamily("Times New Roman").FontSize(10.5f).Underline();
                    });
                });
                column.Item().Row(row =>
                {
                    row.Spacing(5);
                    row.RelativeItem().Text(text =>
                    {
                        text.Span("5) RA Bill No: ").FontFamily("Times New Roman").FontSize(10.5f);
                        text.Span(Model.RABillNumber).FontFamily("Times New Roman").FontSize(10.5f).Underline();
                    });
                });
                column.Item().Row(row =>
                {
                    row.Spacing(5);
                    row.RelativeItem().Text(text =>
                    {
                        text.Span("6) RA bill Amount i.e. Development Credit Note value: ").FontFamily("Times New Roman").FontSize(10.5f);
                        text.Span(Model.RABillAmount.ToString()).FontFamily("Times New Roman").FontSize(10.5f).Underline();
                    });
                });
                column.Item().Row(row =>
                {
                    row.Spacing(5);
                    row.RelativeItem().Text(text =>
                    {
                        text.Span("7) Earlier issued Development Credit Note value: ").FontFamily("Times New Roman").FontSize(10.5f);
                        text.Span(Model.OldIssuedValue.ToString()).FontFamily("Times New Roman").FontSize(10.5f).Underline();
                    });
                });
                column.Item().Row(row =>
                {
                    row.Spacing(5);
                    row.RelativeItem().Text(text =>
                    {
                        text.Span("8) Updated Development Credit Note issued value: ").FontFamily("Times New Roman").FontSize(10.5f);
                        text.Span(Model.NewIssuedValue.ToString()).FontFamily("Times New Roman").FontSize(10.5f).Underline();
                    });
                });

                column.Item().PaddingTop(10).Text(text =>
                {
                    text.Span("With reference to above herewith issued Development Credit Note to the holder of value Rs. ").FontFamily("Times New Roman").FontSize(10.5f);
                    text.Span(Model.RABillAmount.ToString()).FontFamily("Times New Roman").FontSize(10.5f).Underline();
                });
                column.Item().Text(text =>
                {
                    text.Span("(In words ").FontFamily("Times New Roman").FontSize(10.5f);
                    text.Span(Model.RABillAmount.ToString()).FontFamily("Times New Roman").FontSize(10.5f).Underline();
                    text.Span(")").FontFamily("Times New Roman").FontSize(10.5f);
                });
                column.Item().PaddingTop(10).Text("Which can use exchange for depositing all charges of Pune Municipal Corporation for Building Permission Department Charges, Property Tax, Road Digging Charges, Water Supply Charges, Sky Sign Charges, charges payable to Land & Estate Department, Charges payable for Pay & Park Tenders for himself & transferable also.").FontFamily("Times New Roman").FontSize(10.5f);
                column.Item().PaddingTop(10).Row(row =>
                {
                    row.RelativeItem(3).Text("Given under common seal of this").FontFamily("Calibri").FontSize(11f);
                    row.RelativeItem(1).Text("DD").FontFamily("Courier New").FontSize(12f);
                    row.RelativeItem(1).Text("MM").FontFamily("Courier New").FontSize(12f);
                    row.RelativeItem(1).Text("YYYY").FontFamily("Courier New").FontSize(12f);
                });
                column.Item().PaddingTop(70).Row(row =>
                {
                    row.RelativeItem(1);
                    row.RelativeItem(2).Column(column =>
                    {
                        column.Item().AlignCenter().Text("CHIEF ENGINEER").FontFamily("Times New Roman").FontSize(10.5f);
                        column.Item().AlignCenter().Text("PUNE MUNCIPAL CORPORATION").FontFamily("Bookman Old Style").FontSize(10.5f);
                    });
                    row.RelativeItem(2).Column(column =>
                    {
                        column.Item().AlignCenter().Text("MUNICIPAL COMMISIONER").FontFamily("Times New Roman").FontSize(10.5f);
                        column.Item().AlignCenter().Text("PUNE MUNCIPAL CORPORATION").FontFamily("Bookman Old Style").FontSize(10.5f);
                    });
                });

            });
        }
    }

    public static class Mapper
    {
        public static T Map<T>(ExpandoObject source) where T : class
        {
            T destination = (T)Activator.CreateInstance(typeof(T));
            IDictionary<string, object> dict = source;
            var type = destination.GetType();

            foreach (var prop in type.GetProperties())
            {
                var lower = prop.Name.ToLower();
                Console.WriteLine($"Mapping {lower}");
                var key = dict.Keys.SingleOrDefault(k => k.ToLower() == lower);

                if (key != null)
                {
                    prop.SetValue(destination, CastProperty(dict[key], prop.PropertyType.ToString()), null);
                }
            }
            return destination;
        }

        static dynamic CastProperty(dynamic property, string type) {
            switch (type) {
                case "System.String": 
                    return (String)property;
                case "System.Decimal": 
                    return (Decimal)property;
                default:
                    return property;

            }
        }

    }


    public class Model
    {
        public string DCNId { get; set; }
        public string PethName { get; set; }
        public string ResolutionNumber { get; set; }
        public DateTime ResolutionDate { get; set; }
        public string TenderName { get; set; }
        public string VendorName { get; set; }
        public string WorkOrderNumber { get; set; }
        public Decimal TenderAmount { get; set; }
        public string RABillNumber { get; set; }
        public Decimal RABillAmount { get; set; }
        public Decimal OldIssuedValue { get; set; }
        public Decimal NewIssuedValue { get; set; }
        public DateTime IssuedDate { get; set; } = DateTime.Now;
        public String Qr { get; set; }
    }
}