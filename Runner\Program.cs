using System.Data.SqlTypes;
using System.Diagnostics;
using System.Dynamic;
using System.Text;
using System.Text.Json;
using Microsoft.Extensions.Configuration;

var builder = new ConfigurationBuilder()
               .AddJsonFile($"appsettings.json", true, true)
               .AddEnvironmentVariables();

var config = builder.Build();
// try
// {
//     TestRecommendedOrdinaryCertificate();
// }
// catch (Exception ex)
// {

// }

await MembershipCertificatePlugin();

async Task MembershipCertificatePlugin()
{

    var plugin = new Factory.MembershipCertificatePlugin.MembershipCertificatePlugin();
    // var plugin = new Factory.CredaiBankReceiptPlugin.CredaiBankReceiptPlugin();
    // var plugin = new Factory.CredaiCertificatePlugin.CredaiCertificatePlugin();
    dynamic configuration = new ExpandoObject();
    configuration.Logo = Convert.ToBase64String(File.ReadAllBytes("../../../Capture.PNG"));

    plugin.Configure(configuration);
    // dynamic configuration = new ExpandoObject();
    // configuration.BaseUrl = "https://api.authkey.io/request";
    // plugin.Configure(configuration);
    // var phoneNumber = "**********";
    // dynamic input = new ExpandoObject();
    DateTime date = DateTime.Now;
    dynamic inputPdf = new ExpandoObject();
    inputPdf.CertificateNumber = "CREDAI-Pune/25-26/RPM/2175";
    //inputPdf.Type = "Associate";
    //inputPdf.Type = "Rera";
    inputPdf.Type = "Ordinary";
    inputPdf.PresidentSignature = new System.IO.MemoryStream(File.ReadAllBytes("President.png"));
    inputPdf.SecaratorySignature = new System.IO.MemoryStream(File.ReadAllBytes("Secaratory.png"));
    inputPdf.FirmName = "SHYAMALA REALTY";
    inputPdf.FromDate = "01-01-2023";
    inputPdf.ToDate = "01-01-2056";
    inputPdf.President = "Ranjit Naiknavare";
    inputPdf.Secretary = "Ashwin Tricmal";
    inputPdf.ProjectName = "Ramchandra Smruti Co-Operative Housing Society Ltd";
    inputPdf.ToYear = DateTime.Now.AddYears(1).ToString("yyyy");
    inputPdf.Reradate = date.ToString("dd.MM.yyyy");
    inputPdf.ReraValiditydate = date.AddYears(3).ToString("dd.MM.yyyy");
    //     inputPdf.CertificateNumber = "CREDAI-PM/24-25/770";
    // inputPdf.Type = "Rera";
    // inputPdf.ToDate = "01-01-2023";
    // inputPdf.FirmName = fieldsDictionary["FirmName"].ToString();
    // inputPdf.ProjectName = projectName;
    // inputPdf.PresidentSignature = await GetSignatureStreamAsync(entityMetaId, "President");
    // inputPdf.SecaratorySignature = await GetSignatureStreamAsync(entityMetaId, "Secaratory");
    // inputPdf.President = ((IDictionary<string, object>)credaiMasterFields)["President"].ToString();
    // inputPdf.Secretary = ((IDictionary<string, object>)credaiMasterFields)["Secretary"].ToString();


    // input.Type = "Ordinary";
    // input.Address = "dwwdw wdwdwdw dwwwwwwwwwwwwwwwwwwwwwwwww dwwwwwwwwwwwwwwwww wddddddddd wdddddd";
    // input.Name = "Hrishttps://ezbricks.in/logs/container/cd2559a335a1hikesh Pandey knjkn mw;l ededwdwdwdwdwdwdw dddddddd  dwdwdw dwdwdwd dwdwdwd dwdwdwd";
    // DateTime date = DateTime.Now;
    // // input.FromYear = DateTime.Now.ToString("yyyy");
    // input.ToYear = DateTime.Now.AddYears(1).ToString("yyyy");
    // input.FromDate = date.ToString("dd-MM-yyyy");
    // input.ProfilePhoto = new System.IO.MemoryStream(File.ReadAllBytes("Logo.png"));
    // input.QrCodeUrl = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\" \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\"><svg xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\" viewBox=\"0 0 41 41\" stroke=\"none\"><rect width=\"100%\" height=\"100%\" fill=\"#ffffff\"/><path d=\"M4,4h7v1h-7z M14,4h2v1h-2z M21,4h4v1h-4z M26,4h1v4h-1z M27,4h1v1h-1z M30,4h7v1h-7z M4,5h1v6h-1z M10,5h1v6h-1z M13,5h1v5h-1z M15,5h2v3h-2z M18,5h4v1h-4z M24,5h2v1h-2z M28,5h1v1h-1z M30,5h1v6h-1z M36,5h1v6h-1z M6,6h3v3h-3z M12,6h1v6h-1z M18,6h1v1h-1z M25,6h1v2h-1z M32,6h3v3h-3z M19,7h6v1h-6z M14,8h1v1h-1z M17,8h1v2h-1z M21,8h1v2h-1z M24,8h1v3h-1z M27,8h2v1h-2z M16,9h1v2h-1z M18,9h3v1h-3z M25,9h1v1h-1z M28,9h1v2h-1z M5,10h5v1h-5z M14,10h1v1h-1z M18,10h1v1h-1z M20,10h1v1h-1z M22,10h1v2h-1z M26,10h1v3h-1z M31,10h5v1h-5z M15,11h1v1h-1z M19,11h1v2h-1z M23,11h1v1h-1z M25,11h1v1h-1z M4,12h1v3h-1z M6,12h5v1h-5z M14,12h1v4h-1z M17,12h1v4h-1z M18,12h1v1h-1z M21,12h1v1h-1z M30,12h5v1h-5z M5,13h1v1h-1z M7,13h1v1h-1z M12,13h2v1h-2z M16,13h1v2h-1z M20,13h1v2h-1z M23,13h3v1h-3z M27,13h1v1h-1z M30,13h2v1h-2z M33,13h2v1h-2z M36,13h1v1h-1z M10,14h2v1h-2z M15,14h1v1h-1z M18,14h2v1h-2z M21,14h2v1h-2z M25,14h1v4h-1z M26,14h1v1h-1z M29,14h2v1h-2z M32,14h1v3h-1z M34,14h1v2h-1z M8,15h2v1h-2z M12,15h1v1h-1z M19,15h1v1h-1z M22,15h2v2h-2z M24,15h1v1h-1z M28,15h1v2h-1z M33,15h1v7h-1z M35,15h1v4h-1z M5,16h1v2h-1z M10,16h1v1h-1z M13,16h1v1h-1z M18,16h1v1h-1z M20,16h2v1h-2z M26,16h2v1h-2z M29,16h1v1h-1z M31,16h1v1h-1z M36,16h1v2h-1z M8,17h1v2h-1z M16,17h2v1h-2z M24,17h1v3h-1z M27,17h1v1h-1z M30,17h1v3h-1z M4,18h1v1h-1z M6,18h2v1h-2z M9,18h3v1h-3z M13,18h1v2h-1z M19,18h3v1h-3z M29,18h1v3h-1z M31,18h1v7h-1z M32,18h1v1h-1z M5,19h1v2h-1z M7,19h1v1h-1z M11,19h1v2h-1z M16,19h2v1h-2z M19,19h1v2h-1z M22,19h1v1h-1z M25,19h1v1h-1z M28,19h1v2h-1z M34,19h1v1h-1z M8,20h2v2h-2z M10,20h1v1h-1z M12,20h1v1h-1z M15,20h2v1h-2z M18,20h1v3h-1z M21,20h1v1h-1z M23,20h1v2h-1z M26,20h2v1h-2z M32,20h1v1h-1z M36,20h1v2h-1z M4,21h1v8h-1z M7,21h1v3h-1z M14,21h1v3h-1z M20,21h1v1h-1z M22,21h1v4h-1z M25,21h1v1h-1z M27,21h1v1h-1z M30,21h1v3h-1z M34,21h1v3h-1z M5,22h1v2h-1z M10,22h2v1h-2z M15,22h2v1h-2z M19,22h1v3h-1z M24,22h1v1h-1z M28,22h2v1h-2z M32,22h1v3h-1z M35,22h1v3h-1z M6,23h1v2h-1z M12,23h2v1h-2z M17,23h1v4h-1z M20,23h1v2h-1z M23,23h1v2h-1z M25,23h2v2h-2z M29,23h1v2h-1z M33,23h1v2h-1z M36,23h1v3h-1z M8,24h5v1h-5z M15,24h2v1h-2z M21,24h1v2h-1z M28,24h1v1h-1z M5,25h1v1h-1z M8,25h1v3h-1z M9,25h1v1h-1z M14,25h1v1h-1z M18,25h1v4h-1z M24,25h1v1h-1z M30,25h1v1h-1z M34,25h1v3h-1z M6,26h2v1h-2z M10,26h1v1h-1z M12,26h2v1h-2z M16,26h1v2h-1z M19,26h2v1h-2z M22,26h1v1h-1z M25,26h2v1h-2z M29,26h1v3h-1z M32,26h1v8h-1z M33,26h1v3h-1z M35,26h1v3h-1z M6,27h1v1h-1z M9,27h1v2h-1z M11,27h2v1h-2z M14,27h1v1h-1z M20,27h1v4h-1z M25,27h1v1h-1z M27,27h2v5h-2z M10,28h2v1h-2z M13,28h1v3h-1z M15,28h1v1h-1z M17,28h1v1h-1z M21,28h1v1h-1z M26,28h1v1h-1z M30,28h2v1h-2z M36,28h1v2h-1z M12,29h1v1h-1z M14,29h1v1h-1z M16,29h1v1h-1z M22,29h1v4h-1z M23,29h2v1h-2z M34,29h1v3h-1z M4,30h7v1h-7z M17,30h1v3h-1z M19,30h1v5h-1z M21,30h1v1h-1z M24,30h1v1h-1z M26,30h1v1h-1z M30,30h1v1h-1z M35,30h1v2h-1z M4,31h1v6h-1z M10,31h1v6h-1z M12,31h1v4h-1z M14,31h2v1h-2z M23,31h1v1h-1z M25,31h1v2h-1z M33,31h1v2h-1z M36,31h1v1h-1z M6,32h3v3h-3z M13,32h1v2h-1z M15,32h2v2h-2z M18,32h1v3h-1z M20,32h2v2h-2z M24,32h1v4h-1z M26,32h1v5h-1z M28,32h4v1h-4z M29,33h1v1h-1z M34,33h3v1h-3z M14,34h1v1h-1z M17,34h1v1h-1z M27,34h1v1h-1z M30,34h2v1h-2z M13,35h1v1h-1z M23,35h1v2h-1z M25,35h1v1h-1z M32,35h1v1h-1z M34,35h1v1h-1z M5,36h5v1h-5z M12,36h1v1h-1z M14,36h1v1h-1z M17,36h1v1h-1z M19,36h1v1h-1z M21,36h1v1h-1z M27,36h5v1h-5z M33,36h1v1h-1z M35,36h1v1h-1z\" fill=\"#000000\"/></svg>";
    // input.ReraValiditydate = date.AddYears(3).ToString("dd.MM.yyyy");
    // input.CertificateNumber = "CREDAI_APPLICATION_2023_172";
    // input.Name = "Name";
    // var otp = "1234";
    // input.Path = $"?authkey=143ba47df2adb401&mobile={phoneNumber}&country_code=91&sid=9744&Var={otp}";
    // input.Method = "GET";
    // input.Headers = "Content-Type:application/json";
    try
    {
        dynamic output = await plugin.Invoke(inputPdf);
        File.WriteAllBytes("OrdinaryCertificate.pdf", output.Result);
        Process.Start("explorer.exe", "OrdinaryCertificate.pdf");

    }
    catch (Exception ex)
    {
    }

}
async Task CcaAvenueInvoicePlugin()
{
    var plugin = new Factory.CcaAvenueInvoicePlugin.CcaAvenueInvoicePlugin();
    dynamic configuration = new ExpandoObject();
    configuration.Logo = Convert.ToBase64String(File.ReadAllBytes("Black logo - no background (1).png"));

    plugin.Configure(configuration);
    dynamic input = new ExpandoObject();
    input.Total = "1234566";
    input.Name = "fefefe";
    input.Number = "frfr";
    input.Gst = 18;
    input.Total = "590.00";
    dynamic output = await plugin.Invoke(input);
    File.WriteAllBytes("test89.pdf", output.Result);
    Process.Start("explorer.exe", "test89.pdf");
}
async Task TestDCNToBlock()
{
    string resultText = "status=0&enc_response=9c60b9f7f542fb3ac8e416f629bcf80d161816248448e530d68fb92de6f5323c8abba318d63c11dbd5966973c20907e6c6f9222404d59982d084ec513f3b5d691cd7b287f6061cec7277ed56f27828e8767d6019d1dee2b43421a31e048f12d2";
    string subString = "";
    int startIndex = resultText.IndexOf("enc_response=");

    if (startIndex != -1)
    {
        subString = resultText.Substring(startIndex + "enc_response=".Length);
        Console.WriteLine(subString);
    }
    var plugin = new Factory.CcAvenuePlugin.CcAvenuePlugin();
    dynamic input = new ExpandoObject();


    input.Key = "6A518D7F30983C2DFE407DDFB80419A5";
    input.strToEncrypt = "";
    input.strToDecrypt = "31038df6f91148cccf7a87371e6a2769187bf0620f72655626cf0182ca54c67f34a37bcfcebe39724d5d31389f4c0a0ed9a79aeaf364f826cba74429c47b596902a86fabdc4a6107c05ab419bde4521a7cff9123aa147bca1da9b870752f11c6f8d148706fc976e39431e53bd3e44891";
    // var input = new
    // {
    //     DCNId = "DCN_PMC_2022_48",
    //     PethName = "Ghorpudi Peth",
    //     ResolutionNumber = "RNDCN48",
    //     ResolutionDate = "01/01/2022",
    //     TenderName = "Hospital in Kalyani Nagar",
    //     VendorName = "Invimatic Solutions LLP",
    //     WorkOrderNumber = "WODCN48/1",
    //     TenderAmount = 10000000,
    //     RABillNumber = "RADCN48/1",
    //     RABillAmount = 100000,
    //     OldIssuedValue = 0,
    //     NewIssuedValue = 100000,
    //     IssuedValue = 100000,
    //     IssuedValueWords = "One Lakh"
    // };
    var output = await plugin.Invoke(input);
    Console.WriteLine(output.Result);
}

async Task TestDCNCertificate()
{
    var plugin = new Factory.LicenseCertificatePlugin.LicenseCertificatePlugin();
    // plugin.Configure(configuration);
    dynamic input = new ExpandoObject();
    input.DCNId = "PMC_DCN_2022_12";
    input.PethName = "Ghorpudi Peth";
    input.ResolutionNumber = "DCNRS12";
    input.ResolutionDate = DateTime.Now;
    input.TenderName = "Road in Kalyani Nagar";
    input.VendorName = "Invimatic Technologies Private Limited";
    input.WorkOrderNumber = "PMC_DCN_2022_12";
    input.TenderAmount = 1000000;
    input.RABillNumber = "RADCN02";
    input.RABillAmount = 1000;
    input.OldIssuedValue = 0;
    input.NewIssuedValue = 100000;
    input.IssuedValue = 10000;

    dynamic output = await plugin.Invoke(input);
    File.WriteAllBytes("test.pdf", output.Result);
    Process.Start("explorer.exe", "test.pdf");
}

async Task TestRecommendedCertificate()
{
    var plugin = new Factory.RecommendedAssociateCertificatePlugin.RecommendedAssociateCertificatePlugin();
    // plugin.Configure(configuration);
    dynamic input = new ExpandoObject();
    input.Date = "02-11-2023";
    input.Address = "3rd and 4th Floor, Gera-77, Ramwadi, Kalyani Nagar, Pune, Maharashtra 411006";
    input.Name = "Dipali Thakre";

    // input.PethNa-me = "Ghorpudi Peth";
    // input.ResolutionNumber = "DCNRS12";
    // input.ResolutionDate = DateTime.Now;
    // input.TenderName = "Road in Kalyani Nagar";
    // input.VendorName = "Invimatic Technologies Private Limited";
    // input.WorkOrderNumber = "PMC_DCN_2022_12";
    // input.TenderAmount = 1000000;
    // input.RABillNumber = "RADCN02";
    // input.RABillAmount = 1000;
    // input.OldIssuedValue = 0;
    // input.NewIssuedValue = 100000;
    // input.IssuedValue = 10000;
    // input.Qr = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\" \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\"><svg xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\" viewBox=\"0 0 41 41\" stroke=\"none\"><rect width=\"100%\" height=\"100%\" fill=\"#ffffff\"/><path d=\"M4,4h7v1h-7z M14,4h2v1h-2z M21,4h4v1h-4z M26,4h1v4h-1z M27,4h1v1h-1z M30,4h7v1h-7z M4,5h1v6h-1z M10,5h1v6h-1z M13,5h1v5h-1z M15,5h2v3h-2z M18,5h4v1h-4z M24,5h2v1h-2z M28,5h1v1h-1z M30,5h1v6h-1z M36,5h1v6h-1z M6,6h3v3h-3z M12,6h1v6h-1z M18,6h1v1h-1z M25,6h1v2h-1z M32,6h3v3h-3z M19,7h6v1h-6z M14,8h1v1h-1z M17,8h1v2h-1z M21,8h1v2h-1z M24,8h1v3h-1z M27,8h2v1h-2z M16,9h1v2h-1z M18,9h3v1h-3z M25,9h1v1h-1z M28,9h1v2h-1z M5,10h5v1h-5z M14,10h1v1h-1z M18,10h1v1h-1z M20,10h1v1h-1z M22,10h1v2h-1z M26,10h1v3h-1z M31,10h5v1h-5z M15,11h1v1h-1z M19,11h1v2h-1z M23,11h1v1h-1z M25,11h1v1h-1z M4,12h1v3h-1z M6,12h5v1h-5z M14,12h1v4h-1z M17,12h1v4h-1z M18,12h1v1h-1z M21,12h1v1h-1z M30,12h5v1h-5z M5,13h1v1h-1z M7,13h1v1h-1z M12,13h2v1h-2z M16,13h1v2h-1z M20,13h1v2h-1z M23,13h3v1h-3z M27,13h1v1h-1z M30,13h2v1h-2z M33,13h2v1h-2z M36,13h1v1h-1z M10,14h2v1h-2z M15,14h1v1h-1z M18,14h2v1h-2z M21,14h2v1h-2z M25,14h1v4h-1z M26,14h1v1h-1z M29,14h2v1h-2z M32,14h1v3h-1z M34,14h1v2h-1z M8,15h2v1h-2z M12,15h1v1h-1z M19,15h1v1h-1z M22,15h2v2h-2z M24,15h1v1h-1z M28,15h1v2h-1z M33,15h1v7h-1z M35,15h1v4h-1z M5,16h1v2h-1z M10,16h1v1h-1z M13,16h1v1h-1z M18,16h1v1h-1z M20,16h2v1h-2z M26,16h2v1h-2z M29,16h1v1h-1z M31,16h1v1h-1z M36,16h1v2h-1z M8,17h1v2h-1z M16,17h2v1h-2z M24,17h1v3h-1z M27,17h1v1h-1z M30,17h1v3h-1z M4,18h1v1h-1z M6,18h2v1h-2z M9,18h3v1h-3z M13,18h1v2h-1z M19,18h3v1h-3z M29,18h1v3h-1z M31,18h1v7h-1z M32,18h1v1h-1z M5,19h1v2h-1z M7,19h1v1h-1z M11,19h1v2h-1z M16,19h2v1h-2z M19,19h1v2h-1z M22,19h1v1h-1z M25,19h1v1h-1z M28,19h1v2h-1z M34,19h1v1h-1z M8,20h2v2h-2z M10,20h1v1h-1z M12,20h1v1h-1z M15,20h2v1h-2z M18,20h1v3h-1z M21,20h1v1h-1z M23,20h1v2h-1z M26,20h2v1h-2z M32,20h1v1h-1z M36,20h1v2h-1z M4,21h1v8h-1z M7,21h1v3h-1z M14,21h1v3h-1z M20,21h1v1h-1z M22,21h1v4h-1z M25,21h1v1h-1z M27,21h1v1h-1z M30,21h1v3h-1z M34,21h1v3h-1z M5,22h1v2h-1z M10,22h2v1h-2z M15,22h2v1h-2z M19,22h1v3h-1z M24,22h1v1h-1z M28,22h2v1h-2z M32,22h1v3h-1z M35,22h1v3h-1z M6,23h1v2h-1z M12,23h2v1h-2z M17,23h1v4h-1z M20,23h1v2h-1z M23,23h1v2h-1z M25,23h2v2h-2z M29,23h1v2h-1z M33,23h1v2h-1z M36,23h1v3h-1z M8,24h5v1h-5z M15,24h2v1h-2z M21,24h1v2h-1z M28,24h1v1h-1z M5,25h1v1h-1z M8,25h1v3h-1z M9,25h1v1h-1z M14,25h1v1h-1z M18,25h1v4h-1z M24,25h1v1h-1z M30,25h1v1h-1z M34,25h1v3h-1z M6,26h2v1h-2z M10,26h1v1h-1z M12,26h2v1h-2z M16,26h1v2h-1z M19,26h2v1h-2z M22,26h1v1h-1z M25,26h2v1h-2z M29,26h1v3h-1z M32,26h1v8h-1z M33,26h1v3h-1z M35,26h1v3h-1z M6,27h1v1h-1z M9,27h1v2h-1z M11,27h2v1h-2z M14,27h1v1h-1z M20,27h1v4h-1z M25,27h1v1h-1z M27,27h2v5h-2z M10,28h2v1h-2z M13,28h1v3h-1z M15,28h1v1h-1z M17,28h1v1h-1z M21,28h1v1h-1z M26,28h1v1h-1z M30,28h2v1h-2z M36,28h1v2h-1z M12,29h1v1h-1z M14,29h1v1h-1z M16,29h1v1h-1z M22,29h1v4h-1z M23,29h2v1h-2z M34,29h1v3h-1z M4,30h7v1h-7z M17,30h1v3h-1z M19,30h1v5h-1z M21,30h1v1h-1z M24,30h1v1h-1z M26,30h1v1h-1z M30,30h1v1h-1z M35,30h1v2h-1z M4,31h1v6h-1z M10,31h1v6h-1z M12,31h1v4h-1z M14,31h2v1h-2z M23,31h1v1h-1z M25,31h1v2h-1z M33,31h1v2h-1z M36,31h1v1h-1z M6,32h3v3h-3z M13,32h1v2h-1z M15,32h2v2h-2z M18,32h1v3h-1z M20,32h2v2h-2z M24,32h1v4h-1z M26,32h1v5h-1z M28,32h4v1h-4z M29,33h1v1h-1z M34,33h3v1h-3z M14,34h1v1h-1z M17,34h1v1h-1z M27,34h1v1h-1z M30,34h2v1h-2z M13,35h1v1h-1z M23,35h1v2h-1z M25,35h1v1h-1z M32,35h1v1h-1z M34,35h1v1h-1z M5,36h5v1h-5z M12,36h1v1h-1z M14,36h1v1h-1z M17,36h1v1h-1z M19,36h1v1h-1z M21,36h1v1h-1z M27,36h5v1h-5z M33,36h1v1h-1z M35,36h1v1h-1z\" fill=\"#000000\"/></svg>";

    dynamic output = await plugin.Invoke(input);
    File.WriteAllBytes("test.pdf", output.Result);
    Process.Start("explorer.exe", "test.pdf");
}

async Task TestBankReceipt()
{
    var plugin = new Factory.BankReceiptCredAiPlugin.BankReceiptCredAiPlugin();
    // plugin.Configure(configuration);
    dynamic input = new ExpandoObject();
    input.ReceiptNo = "123456";
    input.BankDetails = "Bank of Maharashtra A/c ***********";
    input.EntranceFee = "20000";
    input.SubscriptionName = "Project Subscription";
    input.AnnualSub = "20000";
    input.CompanyName = "Invimatimc";
    input.TrnId = "****************";
    input.GST = "21321";
    input.AmtInWords = "123456";
    input.TotalAmt = "********";


    // input.PethNa-me = "Ghorpudi Peth";
    // input.ResolutionNumber = "DCNRS12";
    // input.ResolutionDate = DateTime.Now;
    // input.TenderName = "Road in Kalyani Nagar";
    // input.VendorName = "Invimatic Technologies Private Limited";
    // input.WorkOrderNumber = "PMC_DCN_2022_12";
    // input.TenderAmount = 1000000;
    // input.RABillNumber = "RADCN02";
    // input.RABillAmount = 1000;
    // input.OldIssuedValue = 0;
    // input.NewIssuedValue = 100000;
    // input.IssuedValue = 10000;
    // input.Qr = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\" \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\"><svg xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\" viewBox=\"0 0 41 41\" stroke=\"none\"><rect width=\"100%\" height=\"100%\" fill=\"#ffffff\"/><path d=\"M4,4h7v1h-7z M14,4h2v1h-2z M21,4h4v1h-4z M26,4h1v4h-1z M27,4h1v1h-1z M30,4h7v1h-7z M4,5h1v6h-1z M10,5h1v6h-1z M13,5h1v5h-1z M15,5h2v3h-2z M18,5h4v1h-4z M24,5h2v1h-2z M28,5h1v1h-1z M30,5h1v6h-1z M36,5h1v6h-1z M6,6h3v3h-3z M12,6h1v6h-1z M18,6h1v1h-1z M25,6h1v2h-1z M32,6h3v3h-3z M19,7h6v1h-6z M14,8h1v1h-1z M17,8h1v2h-1z M21,8h1v2h-1z M24,8h1v3h-1z M27,8h2v1h-2z M16,9h1v2h-1z M18,9h3v1h-3z M25,9h1v1h-1z M28,9h1v2h-1z M5,10h5v1h-5z M14,10h1v1h-1z M18,10h1v1h-1z M20,10h1v1h-1z M22,10h1v2h-1z M26,10h1v3h-1z M31,10h5v1h-5z M15,11h1v1h-1z M19,11h1v2h-1z M23,11h1v1h-1z M25,11h1v1h-1z M4,12h1v3h-1z M6,12h5v1h-5z M14,12h1v4h-1z M17,12h1v4h-1z M18,12h1v1h-1z M21,12h1v1h-1z M30,12h5v1h-5z M5,13h1v1h-1z M7,13h1v1h-1z M12,13h2v1h-2z M16,13h1v2h-1z M20,13h1v2h-1z M23,13h3v1h-3z M27,13h1v1h-1z M30,13h2v1h-2z M33,13h2v1h-2z M36,13h1v1h-1z M10,14h2v1h-2z M15,14h1v1h-1z M18,14h2v1h-2z M21,14h2v1h-2z M25,14h1v4h-1z M26,14h1v1h-1z M29,14h2v1h-2z M32,14h1v3h-1z M34,14h1v2h-1z M8,15h2v1h-2z M12,15h1v1h-1z M19,15h1v1h-1z M22,15h2v2h-2z M24,15h1v1h-1z M28,15h1v2h-1z M33,15h1v7h-1z M35,15h1v4h-1z M5,16h1v2h-1z M10,16h1v1h-1z M13,16h1v1h-1z M18,16h1v1h-1z M20,16h2v1h-2z M26,16h2v1h-2z M29,16h1v1h-1z M31,16h1v1h-1z M36,16h1v2h-1z M8,17h1v2h-1z M16,17h2v1h-2z M24,17h1v3h-1z M27,17h1v1h-1z M30,17h1v3h-1z M4,18h1v1h-1z M6,18h2v1h-2z M9,18h3v1h-3z M13,18h1v2h-1z M19,18h3v1h-3z M29,18h1v3h-1z M31,18h1v7h-1z M32,18h1v1h-1z M5,19h1v2h-1z M7,19h1v1h-1z M11,19h1v2h-1z M16,19h2v1h-2z M19,19h1v2h-1z M22,19h1v1h-1z M25,19h1v1h-1z M28,19h1v2h-1z M34,19h1v1h-1z M8,20h2v2h-2z M10,20h1v1h-1z M12,20h1v1h-1z M15,20h2v1h-2z M18,20h1v3h-1z M21,20h1v1h-1z M23,20h1v2h-1z M26,20h2v1h-2z M32,20h1v1h-1z M36,20h1v2h-1z M4,21h1v8h-1z M7,21h1v3h-1z M14,21h1v3h-1z M20,21h1v1h-1z M22,21h1v4h-1z M25,21h1v1h-1z M27,21h1v1h-1z M30,21h1v3h-1z M34,21h1v3h-1z M5,22h1v2h-1z M10,22h2v1h-2z M15,22h2v1h-2z M19,22h1v3h-1z M24,22h1v1h-1z M28,22h2v1h-2z M32,22h1v3h-1z M35,22h1v3h-1z M6,23h1v2h-1z M12,23h2v1h-2z M17,23h1v4h-1z M20,23h1v2h-1z M23,23h1v2h-1z M25,23h2v2h-2z M29,23h1v2h-1z M33,23h1v2h-1z M36,23h1v3h-1z M8,24h5v1h-5z M15,24h2v1h-2z M21,24h1v2h-1z M28,24h1v1h-1z M5,25h1v1h-1z M8,25h1v3h-1z M9,25h1v1h-1z M14,25h1v1h-1z M18,25h1v4h-1z M24,25h1v1h-1z M30,25h1v1h-1z M34,25h1v3h-1z M6,26h2v1h-2z M10,26h1v1h-1z M12,26h2v1h-2z M16,26h1v2h-1z M19,26h2v1h-2z M22,26h1v1h-1z M25,26h2v1h-2z M29,26h1v3h-1z M32,26h1v8h-1z M33,26h1v3h-1z M35,26h1v3h-1z M6,27h1v1h-1z M9,27h1v2h-1z M11,27h2v1h-2z M14,27h1v1h-1z M20,27h1v4h-1z M25,27h1v1h-1z M27,27h2v5h-2z M10,28h2v1h-2z M13,28h1v3h-1z M15,28h1v1h-1z M17,28h1v1h-1z M21,28h1v1h-1z M26,28h1v1h-1z M30,28h2v1h-2z M36,28h1v2h-1z M12,29h1v1h-1z M14,29h1v1h-1z M16,29h1v1h-1z M22,29h1v4h-1z M23,29h2v1h-2z M34,29h1v3h-1z M4,30h7v1h-7z M17,30h1v3h-1z M19,30h1v5h-1z M21,30h1v1h-1z M24,30h1v1h-1z M26,30h1v1h-1z M30,30h1v1h-1z M35,30h1v2h-1z M4,31h1v6h-1z M10,31h1v6h-1z M12,31h1v4h-1z M14,31h2v1h-2z M23,31h1v1h-1z M25,31h1v2h-1z M33,31h1v2h-1z M36,31h1v1h-1z M6,32h3v3h-3z M13,32h1v2h-1z M15,32h2v2h-2z M18,32h1v3h-1z M20,32h2v2h-2z M24,32h1v4h-1z M26,32h1v5h-1z M28,32h4v1h-4z M29,33h1v1h-1z M34,33h3v1h-3z M14,34h1v1h-1z M17,34h1v1h-1z M27,34h1v1h-1z M30,34h2v1h-2z M13,35h1v1h-1z M23,35h1v2h-1z M25,35h1v1h-1z M32,35h1v1h-1z M34,35h1v1h-1z M5,36h5v1h-5z M12,36h1v1h-1z M14,36h1v1h-1z M17,36h1v1h-1z M19,36h1v1h-1z M21,36h1v1h-1z M27,36h5v1h-5z M33,36h1v1h-1z M35,36h1v1h-1z\" fill=\"#000000\"/></svg>";

    dynamic output = await plugin.Invoke(input);
    File.WriteAllBytes("BankReceipt.pdf", output.Result);
    Process.Start("explorer.exe", "BankReceipt.pdf");
}

async Task TestCertificateCredAi()
{
    var plugin = new Factory.LicenseCertificateCredAiPlugin.LicenseCertificateCredAiPlugin();
    // plugin.Configure(configuration);
    dynamic input = new ExpandoObject();


    input.licenseno = "*********";
    input.validityDate = "07/11/23";
    input.Address = "3rd and 4th Floor, Gera-77, Ramwadi, Kalyani Nagar, Pune, Maharashtra 411006 efjweokf wef efefe fe fe fe fe f e fe fe f ef e fe fwkdwefjeiofjerifjeiofjeriofjeriofjeoifjeri";
    input.Name = "Safalya Holey jewdwje eddeed d edededed edededde";
    input.ProfilePhoto = new System.IO.MemoryStream(File.ReadAllBytes("R.jpg"));
    // input.ResolutionDate = DateTime.Now;
    // input.TenderName = "Road in Kalyani Nagar";
    // input.VendorName = "Invimatic Technologies Private Limited";
    // input.WorkOrderNumber = "PMC_DCN_2022_12";
    // input.TenderAmount = 1000000;
    // input.RABillNumber = "RADCN02";
    // input.RABillAmount = 1000;
    // input.OldIssuedValue = 0;
    // input.NewIssuedValue = 100000;
    // input.IssuedValue = 10000;
    input.QrCodeUrl = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\" \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\"><svg xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\" viewBox=\"0 0 41 41\" stroke=\"none\"><rect width=\"100%\" height=\"100%\" fill=\"#ffffff\"/><path d=\"M4,4h7v1h-7z M14,4h2v1h-2z M21,4h4v1h-4z M26,4h1v4h-1z M27,4h1v1h-1z M30,4h7v1h-7z M4,5h1v6h-1z M10,5h1v6h-1z M13,5h1v5h-1z M15,5h2v3h-2z M18,5h4v1h-4z M24,5h2v1h-2z M28,5h1v1h-1z M30,5h1v6h-1z M36,5h1v6h-1z M6,6h3v3h-3z M12,6h1v6h-1z M18,6h1v1h-1z M25,6h1v2h-1z M32,6h3v3h-3z M19,7h6v1h-6z M14,8h1v1h-1z M17,8h1v2h-1z M21,8h1v2h-1z M24,8h1v3h-1z M27,8h2v1h-2z M16,9h1v2h-1z M18,9h3v1h-3z M25,9h1v1h-1z M28,9h1v2h-1z M5,10h5v1h-5z M14,10h1v1h-1z M18,10h1v1h-1z M20,10h1v1h-1z M22,10h1v2h-1z M26,10h1v3h-1z M31,10h5v1h-5z M15,11h1v1h-1z M19,11h1v2h-1z M23,11h1v1h-1z M25,11h1v1h-1z M4,12h1v3h-1z M6,12h5v1h-5z M14,12h1v4h-1z M17,12h1v4h-1z M18,12h1v1h-1z M21,12h1v1h-1z M30,12h5v1h-5z M5,13h1v1h-1z M7,13h1v1h-1z M12,13h2v1h-2z M16,13h1v2h-1z M20,13h1v2h-1z M23,13h3v1h-3z M27,13h1v1h-1z M30,13h2v1h-2z M33,13h2v1h-2z M36,13h1v1h-1z M10,14h2v1h-2z M15,14h1v1h-1z M18,14h2v1h-2z M21,14h2v1h-2z M25,14h1v4h-1z M26,14h1v1h-1z M29,14h2v1h-2z M32,14h1v3h-1z M34,14h1v2h-1z M8,15h2v1h-2z M12,15h1v1h-1z M19,15h1v1h-1z M22,15h2v2h-2z M24,15h1v1h-1z M28,15h1v2h-1z M33,15h1v7h-1z M35,15h1v4h-1z M5,16h1v2h-1z M10,16h1v1h-1z M13,16h1v1h-1z M18,16h1v1h-1z M20,16h2v1h-2z M26,16h2v1h-2z M29,16h1v1h-1z M31,16h1v1h-1z M36,16h1v2h-1z M8,17h1v2h-1z M16,17h2v1h-2z M24,17h1v3h-1z M27,17h1v1h-1z M30,17h1v3h-1z M4,18h1v1h-1z M6,18h2v1h-2z M9,18h3v1h-3z M13,18h1v2h-1z M19,18h3v1h-3z M29,18h1v3h-1z M31,18h1v7h-1z M32,18h1v1h-1z M5,19h1v2h-1z M7,19h1v1h-1z M11,19h1v2h-1z M16,19h2v1h-2z M19,19h1v2h-1z M22,19h1v1h-1z M25,19h1v1h-1z M28,19h1v2h-1z M34,19h1v1h-1z M8,20h2v2h-2z M10,20h1v1h-1z M12,20h1v1h-1z M15,20h2v1h-2z M18,20h1v3h-1z M21,20h1v1h-1z M23,20h1v2h-1z M26,20h2v1h-2z M32,20h1v1h-1z M36,20h1v2h-1z M4,21h1v8h-1z M7,21h1v3h-1z M14,21h1v3h-1z M20,21h1v1h-1z M22,21h1v4h-1z M25,21h1v1h-1z M27,21h1v1h-1z M30,21h1v3h-1z M34,21h1v3h-1z M5,22h1v2h-1z M10,22h2v1h-2z M15,22h2v1h-2z M19,22h1v3h-1z M24,22h1v1h-1z M28,22h2v1h-2z M32,22h1v3h-1z M35,22h1v3h-1z M6,23h1v2h-1z M12,23h2v1h-2z M17,23h1v4h-1z M20,23h1v2h-1z M23,23h1v2h-1z M25,23h2v2h-2z M29,23h1v2h-1z M33,23h1v2h-1z M36,23h1v3h-1z M8,24h5v1h-5z M15,24h2v1h-2z M21,24h1v2h-1z M28,24h1v1h-1z M5,25h1v1h-1z M8,25h1v3h-1z M9,25h1v1h-1z M14,25h1v1h-1z M18,25h1v4h-1z M24,25h1v1h-1z M30,25h1v1h-1z M34,25h1v3h-1z M6,26h2v1h-2z M10,26h1v1h-1z M12,26h2v1h-2z M16,26h1v2h-1z M19,26h2v1h-2z M22,26h1v1h-1z M25,26h2v1h-2z M29,26h1v3h-1z M32,26h1v8h-1z M33,26h1v3h-1z M35,26h1v3h-1z M6,27h1v1h-1z M9,27h1v2h-1z M11,27h2v1h-2z M14,27h1v1h-1z M20,27h1v4h-1z M25,27h1v1h-1z M27,27h2v5h-2z M10,28h2v1h-2z M13,28h1v3h-1z M15,28h1v1h-1z M17,28h1v1h-1z M21,28h1v1h-1z M26,28h1v1h-1z M30,28h2v1h-2z M36,28h1v2h-1z M12,29h1v1h-1z M14,29h1v1h-1z M16,29h1v1h-1z M22,29h1v4h-1z M23,29h2v1h-2z M34,29h1v3h-1z M4,30h7v1h-7z M17,30h1v3h-1z M19,30h1v5h-1z M21,30h1v1h-1z M24,30h1v1h-1z M26,30h1v1h-1z M30,30h1v1h-1z M35,30h1v2h-1z M4,31h1v6h-1z M10,31h1v6h-1z M12,31h1v4h-1z M14,31h2v1h-2z M23,31h1v1h-1z M25,31h1v2h-1z M33,31h1v2h-1z M36,31h1v1h-1z M6,32h3v3h-3z M13,32h1v2h-1z M15,32h2v2h-2z M18,32h1v3h-1z M20,32h2v2h-2z M24,32h1v4h-1z M26,32h1v5h-1z M28,32h4v1h-4z M29,33h1v1h-1z M34,33h3v1h-3z M14,34h1v1h-1z M17,34h1v1h-1z M27,34h1v1h-1z M30,34h2v1h-2z M13,35h1v1h-1z M23,35h1v2h-1z M25,35h1v1h-1z M32,35h1v1h-1z M34,35h1v1h-1z M5,36h5v1h-5z M12,36h1v1h-1z M14,36h1v1h-1z M17,36h1v1h-1z M19,36h1v1h-1z M21,36h1v1h-1z M27,36h5v1h-5z M33,36h1v1h-1z M35,36h1v1h-1z\" fill=\"#000000\"/></svg>";
    try
    {
        dynamic output = await plugin.Invoke(input);
        File.WriteAllBytes("CredAI certificate.pdf", output.Result);
        Process.Start("explorer.exe", "CredAI certificate.pdf");
    }
    catch (Exception ex)
    {

    }
}
async Task TestRecommendedOrdinaryCertificate()
{
    var plugin = new Factory.RecommendedAssociateCertificatePlugin.RecommendedAssociateCertificatePlugin();
    // plugin.Configure(configuration);
    dynamic input = new ExpandoObject();
    input.Date = "02-11-2023";
    input.Address = "3rd and 4th Floor, Gera-77, Ramwadi, Kalyani Nagar, Pune, Maharashtra 411006";
    input.Name = "Dipali Thakre";
    input.ProjectName = "3rd and 4th Floor, Gera-77, Ramwadi, Kalyani Nagar, Pune, Maharashtra 411006";
    input.Location = "3rd and 4th Floor, Gera-77, Ramwadi, Kalyani Nagar, Pune, Maharashtra 411006 vddfvfvdfvdfvdfvdvdfvdf";
    input.UnitInProject = "Residential:0";
    // input.DateOfPayment = DateTime.Now;
    input.ProprietorOrPartnerNameOrDirectors = "Ajay Ramkumar Agrawal ,Dinesh Mahendrakumar Agrawal";
    input.FirmAddress = "3rd and 4th Floor, Gera-77, Ramwadi, Kalyani Nagar, Pune, Maharashtra 411006 vddfvfvdfvdfvdfvdvdfvdf";
    input.BuilderName = "Chirag Builder";
    input.ChqNo = "321456";
    input.Amt = "8260";
    //input.ReceiptNo= "4342423";
    input.Type = "Associate";
    input.ProposerName = "r4r4r";
    input.NoOfPlotProject = "5";
    input.NoOfCommercialProject = "9";
    input.NoOfResidentialProject = "19";
    input.PanCardNumber = "9000000000000";
    input.GstNumber = "9000000000000";
    // NoOfResidentialProject
    input.SecondaryName = "frfr";
    input.Firm = "Pinnncal constrictions";
    input.ApplicantId = "RRFRGRG";
    input.CheckedBy = "Ms.U.K. Julka";
    input.PreparedBy = input.Type == "Rera" ? "Santosh Takbhate" : "Shruti Shende";
    input.DirectorGeneral = "Dr.D.K.Abhyankar";
    input.Chairman = "J.P. Shroff";
    input.Secretary = "Ashwin Trimal";
    input.President = "Ranjit Naiknavare";
    input.Director = "Ranjit Naiknavare";
    input.ApprovedMcmDate = "23-09-1323";
    input.MembershipId = "mnm";
    input.IsDirectorGeneral = true;
    input.SecratorySignature = new System.IO.MemoryStream(File.ReadAllBytes("Secaratory.png"));
    input.DgSignature = new System.IO.MemoryStream(File.ReadAllBytes("Scan_Sign-_Ranjit_Naiknavare_page-0001-removebg-preview.png"));
    input.DgDesignateSignature = new System.IO.MemoryStream(File.ReadAllBytes("Scan_Sign-_Ranjit_Naiknavare_page-0001-removebg-preview.png"));
    input.VerifiedBySignature = new System.IO.MemoryStream(File.ReadAllBytes("Scan_Sign-_Ranjit_Naiknavare_page-0001-removebg-preview.png"));
    input.ConvernorSignature = new System.IO.MemoryStream(File.ReadAllBytes("Scan_Sign-_Ranjit_Naiknavare_page-0001-removebg-preview.png"));

    // input.PethNa-me = "Ghorpudi Peth";
    // input.ResolutionNumber = "DCNRS12";
    // input.ResolutionDate = DateTime.Now;
    // input.TenderName = "Road in Kalyani Nagar";
    // input.VendorName = "Invimatic Technologies Private Limited";
    // input.WorkOrderNumber = "PMC_DCN_2022_12";
    // input.TenderAmount = 1000000;
    // input.RABillNumber = "RADCN02";
    // input.RABillAmount = 1000;
    // input.OldIssuedValue = 0;
    // input.NewIssuedValue = 100000;
    // input.IssuedValue = 10000;
    // input.Qr = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\" \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\"><svg xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\" viewBox=\"0 0 41 41\" stroke=\"none\"><rect width=\"100%\" height=\"100%\" fill=\"#ffffff\"/><path d=\"M4,4h7v1h-7z M14,4h2v1h-2z M21,4h4v1h-4z M26,4h1v4h-1z M27,4h1v1h-1z M30,4h7v1h-7z M4,5h1v6h-1z M10,5h1v6h-1z M13,5h1v5h-1z M15,5h2v3h-2z M18,5h4v1h-4z M24,5h2v1h-2z M28,5h1v1h-1z M30,5h1v6h-1z M36,5h1v6h-1z M6,6h3v3h-3z M12,6h1v6h-1z M18,6h1v1h-1z M25,6h1v2h-1z M32,6h3v3h-3z M19,7h6v1h-6z M14,8h1v1h-1z M17,8h1v2h-1z M21,8h1v2h-1z M24,8h1v3h-1z M27,8h2v1h-2z M16,9h1v2h-1z M18,9h3v1h-3z M25,9h1v1h-1z M28,9h1v2h-1z M5,10h5v1h-5z M14,10h1v1h-1z M18,10h1v1h-1z M20,10h1v1h-1z M22,10h1v2h-1z M26,10h1v3h-1z M31,10h5v1h-5z M15,11h1v1h-1z M19,11h1v2h-1z M23,11h1v1h-1z M25,11h1v1h-1z M4,12h1v3h-1z M6,12h5v1h-5z M14,12h1v4h-1z M17,12h1v4h-1z M18,12h1v1h-1z M21,12h1v1h-1z M30,12h5v1h-5z M5,13h1v1h-1z M7,13h1v1h-1z M12,13h2v1h-2z M16,13h1v2h-1z M20,13h1v2h-1z M23,13h3v1h-3z M27,13h1v1h-1z M30,13h2v1h-2z M33,13h2v1h-2z M36,13h1v1h-1z M10,14h2v1h-2z M15,14h1v1h-1z M18,14h2v1h-2z M21,14h2v1h-2z M25,14h1v4h-1z M26,14h1v1h-1z M29,14h2v1h-2z M32,14h1v3h-1z M34,14h1v2h-1z M8,15h2v1h-2z M12,15h1v1h-1z M19,15h1v1h-1z M22,15h2v2h-2z M24,15h1v1h-1z M28,15h1v2h-1z M33,15h1v7h-1z M35,15h1v4h-1z M5,16h1v2h-1z M10,16h1v1h-1z M13,16h1v1h-1z M18,16h1v1h-1z M20,16h2v1h-2z M26,16h2v1h-2z M29,16h1v1h-1z M31,16h1v1h-1z M36,16h1v2h-1z M8,17h1v2h-1z M16,17h2v1h-2z M24,17h1v3h-1z M27,17h1v1h-1z M30,17h1v3h-1z M4,18h1v1h-1z M6,18h2v1h-2z M9,18h3v1h-3z M13,18h1v2h-1z M19,18h3v1h-3z M29,18h1v3h-1z M31,18h1v7h-1z M32,18h1v1h-1z M5,19h1v2h-1z M7,19h1v1h-1z M11,19h1v2h-1z M16,19h2v1h-2z M19,19h1v2h-1z M22,19h1v1h-1z M25,19h1v1h-1z M28,19h1v2h-1z M34,19h1v1h-1z M8,20h2v2h-2z M10,20h1v1h-1z M12,20h1v1h-1z M15,20h2v1h-2z M18,20h1v3h-1z M21,20h1v1h-1z M23,20h1v2h-1z M26,20h2v1h-2z M32,20h1v1h-1z M36,20h1v2h-1z M4,21h1v8h-1z M7,21h1v3h-1z M14,21h1v3h-1z M20,21h1v1h-1z M22,21h1v4h-1z M25,21h1v1h-1z M27,21h1v1h-1z M30,21h1v3h-1z M34,21h1v3h-1z M5,22h1v2h-1z M10,22h2v1h-2z M15,22h2v1h-2z M19,22h1v3h-1z M24,22h1v1h-1z M28,22h2v1h-2z M32,22h1v3h-1z M35,22h1v3h-1z M6,23h1v2h-1z M12,23h2v1h-2z M17,23h1v4h-1z M20,23h1v2h-1z M23,23h1v2h-1z M25,23h2v2h-2z M29,23h1v2h-1z M33,23h1v2h-1z M36,23h1v3h-1z M8,24h5v1h-5z M15,24h2v1h-2z M21,24h1v2h-1z M28,24h1v1h-1z M5,25h1v1h-1z M8,25h1v3h-1z M9,25h1v1h-1z M14,25h1v1h-1z M18,25h1v4h-1z M24,25h1v1h-1z M30,25h1v1h-1z M34,25h1v3h-1z M6,26h2v1h-2z M10,26h1v1h-1z M12,26h2v1h-2z M16,26h1v2h-1z M19,26h2v1h-2z M22,26h1v1h-1z M25,26h2v1h-2z M29,26h1v3h-1z M32,26h1v8h-1z M33,26h1v3h-1z M35,26h1v3h-1z M6,27h1v1h-1z M9,27h1v2h-1z M11,27h2v1h-2z M14,27h1v1h-1z M20,27h1v4h-1z M25,27h1v1h-1z M27,27h2v5h-2z M10,28h2v1h-2z M13,28h1v3h-1z M15,28h1v1h-1z M17,28h1v1h-1z M21,28h1v1h-1z M26,28h1v1h-1z M30,28h2v1h-2z M36,28h1v2h-1z M12,29h1v1h-1z M14,29h1v1h-1z M16,29h1v1h-1z M22,29h1v4h-1z M23,29h2v1h-2z M34,29h1v3h-1z M4,30h7v1h-7z M17,30h1v3h-1z M19,30h1v5h-1z M21,30h1v1h-1z M24,30h1v1h-1z M26,30h1v1h-1z M30,30h1v1h-1z M35,30h1v2h-1z M4,31h1v6h-1z M10,31h1v6h-1z M12,31h1v4h-1z M14,31h2v1h-2z M23,31h1v1h-1z M25,31h1v2h-1z M33,31h1v2h-1z M36,31h1v1h-1z M6,32h3v3h-3z M13,32h1v2h-1z M15,32h2v2h-2z M18,32h1v3h-1z M20,32h2v2h-2z M24,32h1v4h-1z M26,32h1v5h-1z M28,32h4v1h-4z M29,33h1v1h-1z M34,33h3v1h-3z M14,34h1v1h-1z M17,34h1v1h-1z M27,34h1v1h-1z M30,34h2v1h-2z M13,35h1v1h-1z M23,35h1v2h-1z M25,35h1v1h-1z M32,35h1v1h-1z M34,35h1v1h-1z M5,36h5v1h-5z M12,36h1v1h-1z M14,36h1v1h-1z M17,36h1v1h-1z M19,36h1v1h-1z M21,36h1v1h-1z M27,36h5v1h-5z M33,36h1v1h-1z M35,36h1v1h-1z\" fill=\"#000000\"/></svg>";

    dynamic output = await plugin.Invoke(input);
    File.WriteAllBytes("test.pdf", output.Result);
    Process.Start("explorer.exe", "test.pdf");
}
// async Task TestInvoicePlugin()
// {
//     var plugin = new Factory.InvoicePlugin.InvoicePlugin();
//     // plugin.Configure(configuration);
//     dynamic input = new ExpandoObject();


//     //  input.InvoiceNumber = "*********";
//     //  input.DateTime = "07/11/23";
//     //   input.PaymentSource = "3rd and 4th Floor, Gera-77, Ramwadi, Kalyani Nagar, Pune, Maharashtra 411006";
//     //  input.Name = "Safalya Holey";
//     // input.ResolutionDate = DateTime.Now;
//     // input.TenderName = "Road in Kalyani Nagar";
//     // input.VendorName = "Invimatic Technologies Private Limited";
//     // input.WorkOrderNumber = "PMC_DCN_2022_12";
//     // input.TenderAmount = 1000000;
//     // input.RABillNumber = "RADCN02";
//     // input.RABillAmount = 1000;
//     // input.OldIssuedValue = 0;
//     // input.NewIssuedValue = 100000;
//     // input.IssuedValue = 10000;
//     // input.Qr = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\" \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\"><svg xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\" viewBox=\"0 0 41 41\" stroke=\"none\"><rect width=\"100%\" height=\"100%\" fill=\"#ffffff\"/><path d=\"M4,4h7v1h-7z M14,4h2v1h-2z M21,4h4v1h-4z M26,4h1v4h-1z M27,4h1v1h-1z M30,4h7v1h-7z M4,5h1v6h-1z M10,5h1v6h-1z M13,5h1v5h-1z M15,5h2v3h-2z M18,5h4v1h-4z M24,5h2v1h-2z M28,5h1v1h-1z M30,5h1v6h-1z M36,5h1v6h-1z M6,6h3v3h-3z M12,6h1v6h-1z M18,6h1v1h-1z M25,6h1v2h-1z M32,6h3v3h-3z M19,7h6v1h-6z M14,8h1v1h-1z M17,8h1v2h-1z M21,8h1v2h-1z M24,8h1v3h-1z M27,8h2v1h-2z M16,9h1v2h-1z M18,9h3v1h-3z M25,9h1v1h-1z M28,9h1v2h-1z M5,10h5v1h-5z M14,10h1v1h-1z M18,10h1v1h-1z M20,10h1v1h-1z M22,10h1v2h-1z M26,10h1v3h-1z M31,10h5v1h-5z M15,11h1v1h-1z M19,11h1v2h-1z M23,11h1v1h-1z M25,11h1v1h-1z M4,12h1v3h-1z M6,12h5v1h-5z M14,12h1v4h-1z M17,12h1v4h-1z M18,12h1v1h-1z M21,12h1v1h-1z M30,12h5v1h-5z M5,13h1v1h-1z M7,13h1v1h-1z M12,13h2v1h-2z M16,13h1v2h-1z M20,13h1v2h-1z M23,13h3v1h-3z M27,13h1v1h-1z M30,13h2v1h-2z M33,13h2v1h-2z M36,13h1v1h-1z M10,14h2v1h-2z M15,14h1v1h-1z M18,14h2v1h-2z M21,14h2v1h-2z M25,14h1v4h-1z M26,14h1v1h-1z M29,14h2v1h-2z M32,14h1v3h-1z M34,14h1v2h-1z M8,15h2v1h-2z M12,15h1v1h-1z M19,15h1v1h-1z M22,15h2v2h-2z M24,15h1v1h-1z M28,15h1v2h-1z M33,15h1v7h-1z M35,15h1v4h-1z M5,16h1v2h-1z M10,16h1v1h-1z M13,16h1v1h-1z M18,16h1v1h-1z M20,16h2v1h-2z M26,16h2v1h-2z M29,16h1v1h-1z M31,16h1v1h-1z M36,16h1v2h-1z M8,17h1v2h-1z M16,17h2v1h-2z M24,17h1v3h-1z M27,17h1v1h-1z M30,17h1v3h-1z M4,18h1v1h-1z M6,18h2v1h-2z M9,18h3v1h-3z M13,18h1v2h-1z M19,18h3v1h-3z M29,18h1v3h-1z M31,18h1v7h-1z M32,18h1v1h-1z M5,19h1v2h-1z M7,19h1v1h-1z M11,19h1v2h-1z M16,19h2v1h-2z M19,19h1v2h-1z M22,19h1v1h-1z M25,19h1v1h-1z M28,19h1v2h-1z M34,19h1v1h-1z M8,20h2v2h-2z M10,20h1v1h-1z M12,20h1v1h-1z M15,20h2v1h-2z M18,20h1v3h-1z M21,20h1v1h-1z M23,20h1v2h-1z M26,20h2v1h-2z M32,20h1v1h-1z M36,20h1v2h-1z M4,21h1v8h-1z M7,21h1v3h-1z M14,21h1v3h-1z M20,21h1v1h-1z M22,21h1v4h-1z M25,21h1v1h-1z M27,21h1v1h-1z M30,21h1v3h-1z M34,21h1v3h-1z M5,22h1v2h-1z M10,22h2v1h-2z M15,22h2v1h-2z M19,22h1v3h-1z M24,22h1v1h-1z M28,22h2v1h-2z M32,22h1v3h-1z M35,22h1v3h-1z M6,23h1v2h-1z M12,23h2v1h-2z M17,23h1v4h-1z M20,23h1v2h-1z M23,23h1v2h-1z M25,23h2v2h-2z M29,23h1v2h-1z M33,23h1v2h-1z M36,23h1v3h-1z M8,24h5v1h-5z M15,24h2v1h-2z M21,24h1v2h-1z M28,24h1v1h-1z M5,25h1v1h-1z M8,25h1v3h-1z M9,25h1v1h-1z M14,25h1v1h-1z M18,25h1v4h-1z M24,25h1v1h-1z M30,25h1v1h-1z M34,25h1v3h-1z M6,26h2v1h-2z M10,26h1v1h-1z M12,26h2v1h-2z M16,26h1v2h-1z M19,26h2v1h-2z M22,26h1v1h-1z M25,26h2v1h-2z M29,26h1v3h-1z M32,26h1v8h-1z M33,26h1v3h-1z M35,26h1v3h-1z M6,27h1v1h-1z M9,27h1v2h-1z M11,27h2v1h-2z M14,27h1v1h-1z M20,27h1v4h-1z M25,27h1v1h-1z M27,27h2v5h-2z M10,28h2v1h-2z M13,28h1v3h-1z M15,28h1v1h-1z M17,28h1v1h-1z M21,28h1v1h-1z M26,28h1v1h-1z M30,28h2v1h-2z M36,28h1v2h-1z M12,29h1v1h-1z M14,29h1v1h-1z M16,29h1v1h-1z M22,29h1v4h-1z M23,29h2v1h-2z M34,29h1v3h-1z M4,30h7v1h-7z M17,30h1v3h-1z M19,30h1v5h-1z M21,30h1v1h-1z M24,30h1v1h-1z M26,30h1v1h-1z M30,30h1v1h-1z M35,30h1v2h-1z M4,31h1v6h-1z M10,31h1v6h-1z M12,31h1v4h-1z M14,31h2v1h-2z M23,31h1v1h-1z M25,31h1v2h-1z M33,31h1v2h-1z M36,31h1v1h-1z M6,32h3v3h-3z M13,32h1v2h-1z M15,32h2v2h-2z M18,32h1v3h-1z M20,32h2v2h-2z M24,32h1v4h-1z M26,32h1v5h-1z M28,32h4v1h-4z M29,33h1v1h-1z M34,33h3v1h-3z M14,34h1v1h-1z M17,34h1v1h-1z M27,34h1v1h-1z M30,34h2v1h-2z M13,35h1v1h-1z M23,35h1v2h-1z M25,35h1v1h-1z M32,35h1v1h-1z M34,35h1v1h-1z M5,36h5v1h-5z M12,36h1v1h-1z M14,36h1v1h-1z M17,36h1v1h-1z M19,36h1v1h-1z M21,36h1v1h-1z M27,36h5v1h-5z M33,36h1v1h-1z M35,36h1v1h-1z\" fill=\"#000000\"/></svg>";

//     dynamic output = await plugin.Invoke(input);
//     File.WriteAllBytes("CredAI certificate.pdf", output.Result);
//     Process.Start("explorer.exe", "CredAI certificate.pdf");
// }

// async Task TestRecommendedOrdinaryCertificate()
// {
//     var plugin = new Factory.RecommendedOrdinaryCertificatePlugin.RecommendedOrdinaryCertificatePlugin();
//     // plugin.Configure(configuration);
//     dynamic input = new ExpandoObject();
//     input.Date = "02-11-2023";
//     input.Address = "3rd and 4th Floor, Gera-77, Ramwadi, Kalyani Nagar, Pune, Maharashtra 411006";
//     input.Name = "Dipali Thakre";
//     input.PartnerName = "Mayur Macchi";
//     input.BuilderName = "Chirag Builder";
//     input.ChqNo = "321456";
//     input.Amt = "120000";
//     // input.PethNa-me = "Ghorpudi Peth";
//     // input.ResolutionNumber = "DCNRS12";
//     // input.ResolutionDate = DateTime.Now;
//     // input.TenderName = "Road in Kalyani Nagar";
//     // input.VendorName = "Invimatic Technologies Private Limited";
//     // input.WorkOrderNumber = "PMC_DCN_2022_12";
//     // input.TenderAmount = 1000000;
//     // input.RABillNumber = "RADCN02";
//     // input.RABillAmount = 1000;
//     // input.OldIssuedValue = 0;
//     // input.NewIssuedValue = 100000;
//     // input.IssuedValue = 10000;
//     // input.Qr = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\" \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\"><svg xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\" viewBox=\"0 0 41 41\" stroke=\"none\"><rect width=\"100%\" height=\"100%\" fill=\"#ffffff\"/><path d=\"M4,4h7v1h-7z M14,4h2v1h-2z M21,4h4v1h-4z M26,4h1v4h-1z M27,4h1v1h-1z M30,4h7v1h-7z M4,5h1v6h-1z M10,5h1v6h-1z M13,5h1v5h-1z M15,5h2v3h-2z M18,5h4v1h-4z M24,5h2v1h-2z M28,5h1v1h-1z M30,5h1v6h-1z M36,5h1v6h-1z M6,6h3v3h-3z M12,6h1v6h-1z M18,6h1v1h-1z M25,6h1v2h-1z M32,6h3v3h-3z M19,7h6v1h-6z M14,8h1v1h-1z M17,8h1v2h-1z M21,8h1v2h-1z M24,8h1v3h-1z M27,8h2v1h-2z M16,9h1v2h-1z M18,9h3v1h-3z M25,9h1v1h-1z M28,9h1v2h-1z M5,10h5v1h-5z M14,10h1v1h-1z M18,10h1v1h-1z M20,10h1v1h-1z M22,10h1v2h-1z M26,10h1v3h-1z M31,10h5v1h-5z M15,11h1v1h-1z M19,11h1v2h-1z M23,11h1v1h-1z M25,11h1v1h-1z M4,12h1v3h-1z M6,12h5v1h-5z M14,12h1v4h-1z M17,12h1v4h-1z M18,12h1v1h-1z M21,12h1v1h-1z M30,12h5v1h-5z M5,13h1v1h-1z M7,13h1v1h-1z M12,13h2v1h-2z M16,13h1v2h-1z M20,13h1v2h-1z M23,13h3v1h-3z M27,13h1v1h-1z M30,13h2v1h-2z M33,13h2v1h-2z M36,13h1v1h-1z M10,14h2v1h-2z M15,14h1v1h-1z M18,14h2v1h-2z M21,14h2v1h-2z M25,14h1v4h-1z M26,14h1v1h-1z M29,14h2v1h-2z M32,14h1v3h-1z M34,14h1v2h-1z M8,15h2v1h-2z M12,15h1v1h-1z M19,15h1v1h-1z M22,15h2v2h-2z M24,15h1v1h-1z M28,15h1v2h-1z M33,15h1v7h-1z M35,15h1v4h-1z M5,16h1v2h-1z M10,16h1v1h-1z M13,16h1v1h-1z M18,16h1v1h-1z M20,16h2v1h-2z M26,16h2v1h-2z M29,16h1v1h-1z M31,16h1v1h-1z M36,16h1v2h-1z M8,17h1v2h-1z M16,17h2v1h-2z M24,17h1v3h-1z M27,17h1v1h-1z M30,17h1v3h-1z M4,18h1v1h-1z M6,18h2v1h-2z M9,18h3v1h-3z M13,18h1v2h-1z M19,18h3v1h-3z M29,18h1v3h-1z M31,18h1v7h-1z M32,18h1v1h-1z M5,19h1v2h-1z M7,19h1v1h-1z M11,19h1v2h-1z M16,19h2v1h-2z M19,19h1v2h-1z M22,19h1v1h-1z M25,19h1v1h-1z M28,19h1v2h-1z M34,19h1v1h-1z M8,20h2v2h-2z M10,20h1v1h-1z M12,20h1v1h-1z M15,20h2v1h-2z M18,20h1v3h-1z M21,20h1v1h-1z M23,20h1v2h-1z M26,20h2v1h-2z M32,20h1v1h-1z M36,20h1v2h-1z M4,21h1v8h-1z M7,21h1v3h-1z M14,21h1v3h-1z M20,21h1v1h-1z M22,21h1v4h-1z M25,21h1v1h-1z M27,21h1v1h-1z M30,21h1v3h-1z M34,21h1v3h-1z M5,22h1v2h-1z M10,22h2v1h-2z M15,22h2v1h-2z M19,22h1v3h-1z M24,22h1v1h-1z M28,22h2v1h-2z M32,22h1v3h-1z M35,22h1v3h-1z M6,23h1v2h-1z M12,23h2v1h-2z M17,23h1v4h-1z M20,23h1v2h-1z M23,23h1v2h-1z M25,23h2v2h-2z M29,23h1v2h-1z M33,23h1v2h-1z M36,23h1v3h-1z M8,24h5v1h-5z M15,24h2v1h-2z M21,24h1v2h-1z M28,24h1v1h-1z M5,25h1v1h-1z M8,25h1v3h-1z M9,25h1v1h-1z M14,25h1v1h-1z M18,25h1v4h-1z M24,25h1v1h-1z M30,25h1v1h-1z M34,25h1v3h-1z M6,26h2v1h-2z M10,26h1v1h-1z M12,26h2v1h-2z M16,26h1v2h-1z M19,26h2v1h-2z M22,26h1v1h-1z M25,26h2v1h-2z M29,26h1v3h-1z M32,26h1v8h-1z M33,26h1v3h-1z M35,26h1v3h-1z M6,27h1v1h-1z M9,27h1v2h-1z M11,27h2v1h-2z M14,27h1v1h-1z M20,27h1v4h-1z M25,27h1v1h-1z M27,27h2v5h-2z M10,28h2v1h-2z M13,28h1v3h-1z M15,28h1v1h-1z M17,28h1v1h-1z M21,28h1v1h-1z M26,28h1v1h-1z M30,28h2v1h-2z M36,28h1v2h-1z M12,29h1v1h-1z M14,29h1v1h-1z M16,29h1v1h-1z M22,29h1v4h-1z M23,29h2v1h-2z M34,29h1v3h-1z M4,30h7v1h-7z M17,30h1v3h-1z M19,30h1v5h-1z M21,30h1v1h-1z M24,30h1v1h-1z M26,30h1v1h-1z M30,30h1v1h-1z M35,30h1v2h-1z M4,31h1v6h-1z M10,31h1v6h-1z M12,31h1v4h-1z M14,31h2v1h-2z M23,31h1v1h-1z M25,31h1v2h-1z M33,31h1v2h-1z M36,31h1v1h-1z M6,32h3v3h-3z M13,32h1v2h-1z M15,32h2v2h-2z M18,32h1v3h-1z M20,32h2v2h-2z M24,32h1v4h-1z M26,32h1v5h-1z M28,32h4v1h-4z M29,33h1v1h-1z M34,33h3v1h-3z M14,34h1v1h-1z M17,34h1v1h-1z M27,34h1v1h-1z M30,34h2v1h-2z M13,35h1v1h-1z M23,35h1v2h-1z M25,35h1v1h-1z M32,35h1v1h-1z M34,35h1v1h-1z M5,36h5v1h-5z M12,36h1v1h-1z M14,36h1v1h-1z M17,36h1v1h-1z M19,36h1v1h-1z M21,36h1v1h-1z M27,36h5v1h-5z M33,36h1v1h-1z M35,36h1v1h-1z\" fill=\"#000000\"/></svg>";

//     dynamic output = await plugin.Invoke(input);
//     File.WriteAllBytes("test.pdf", output.Result);
//     Process.Start("explorer.exe", "test.pdf");
// }

async Task TestGlobalSMS()
{
    dynamic configuration = new ExpandoObject();
    configuration.ApiKey = "92c45ad439ed4ced902d408d18322402";
    configuration.SenderId = "global";
    var plugin = new Factory.GlobalSMSPlugin.GlobalSMSPlugin();
    plugin.Configure(configuration);
    await plugin.Notify(new List<string>() { "7722033121" }, "", "Test body", File.ReadAllBytes("test.pdf"));
}

// async Task abc()
// {
//  var plugin = new Factory.PMCRMSForm.PMCRMSForm();
// }

async Task TestQRCode()
{
    var plugin = new Factory.QRCodePlugin.QRCodePlugin();
    dynamic input = new ExpandoObject();
    input.Payload = "https://entityblock.invimatic.com/qr?blockId=DCN_PMC_2022_19";
    var output = await plugin.Invoke(input);
    Console.WriteLine(output.Result);

}

async Task TestSEwCertificate()
{
    var plugin = new Factory.SECertificatePlugin.SECertificatePlugin();
    dynamic input = new ExpandoObject();
    input.CertificateNumber = "PMC_RMS_2023_1";
    input.Name = "Veera Raghava Reddy";
    input.Address = "305, Gera 77, Kalyani Nagar, Pune-6";
    input.FromDate = DateTime.Now;
    input.ToYear = 2026;

    dynamic output = await plugin.Invoke(input);
    File.WriteAllBytes("test.pdf", output.Result);
    Process.Start("explorer.exe", "test.pdf");
}

async Task TestSECertificate()
{
    var plugin = new Factory.SECertificatePlugin.SECertificatePlugin();
    dynamic input = new ExpandoObject();
    input.CertificateNumber = "PMC_RMS_2023_1";
    input.Name = "Veera Raghava Reddy";
    input.Address = "305, Gera 77, Kalyani Nagar, Pune-6";
    input.FromDate = DateTime.Now;
    input.ToYear = 2026;

    dynamic output = await plugin.Invoke(input);
    File.WriteAllBytes("test.pdf", output.Result);
    Process.Start("explorer.exe", "test.pdf");
}

async Task TestSMTP()
{
    dynamic configuration = new ExpandoObject();
    configuration.Server = "smtp-relay.sendinblue.com";
    configuration.Port = 587;
    configuration.Username = "<EMAIL>";
    configuration.Password = "RCYkzy7atOEP3cwK";
    configuration.FromAddress = "<EMAIL>";
    configuration.FromName = "Abhishek";
    configuration.UseSSL = true;
    var plugin = new Factory.SMTPPlugin.SMTPPlugin();
    plugin.Configure(configuration);
    plugin.Notify(new List<string>() { "<EMAIL>", "<EMAIL>" }, "Test mail", "Test body", File.ReadAllBytes("test.pdf"));
}

async Task TestHSMSigning()
{
    var plugin = new Factory.RMSHSMPlugin.RMSHSMPlugin();
    dynamic input = new ExpandoObject();
    input.Transaction = "PMC_RMS_007";
    input.KeyLabel = "TestSign19";
    input.File = File.ReadAllBytes("test.pdf");
    input.Coordinates = "87,257,227,206";
    input.OTPType = "single";
    input.OTP = "090728";
    input.CoSign = true;
    dynamic output = await plugin.Invoke(input);
    File.WriteAllBytes("test2.pdf", (byte[])output.Result);
    Process.Start("explorer.exe", "test2.pdf");
}

async Task TestHSMSigning2()
{
    var plugin = new Factory.RMSHSMPlugin.RMSHSMPlugin();
    dynamic input = new ExpandoObject();
    input.Transaction = "PMC_RMS_007_02";
    input.KeyLabel = "TestSign19";
    input.File = File.ReadAllBytes("test2.pdf");
    input.Coordinates = "364,251,490,204";
    input.OTPType = "single";
    input.OTP = "379533";
    input.CoSign = true;
    dynamic output = await plugin.Invoke(input);
    File.WriteAllBytes("test3.pdf", (byte[])output.Result);
    Process.Start("explorer.exe", "test3.pdf");
}

async Task TestHSMSigning3()
{
    var plugin = new Factory.RMSHSMPlugin.RMSHSMPlugin();
    dynamic input = new ExpandoObject();
    input.Transaction = "PMC_RMS_004_2";
    input.KeyLabel = "23359";
    input.File = File.ReadAllBytes("test3.pdf");
    input.Coordinates = "307,253,413,209";
    input.OTPType = "single";
    input.OTP = "448248";
    input.CoSign = true;
    dynamic output = await plugin.Invoke(input);
    File.WriteAllBytes("test4.pdf", (byte[])output.Result);
    Process.Start("explorer.exe", "test2.pdf");
}

async Task TestHSMSigning4()
{
    var plugin = new Factory.RMSHSMPlugin.RMSHSMPlugin();
    dynamic input = new ExpandoObject();
    input.Transaction = "PMC_RMS_003";
    input.KeyLabel = "23359";
    input.File = File.ReadAllBytes("test4.pdf");
    input.Coordinates = "440,253,546,209";
    input.OTPType = "single";
    input.OTP = "448248";
    input.CoSign = false;
    dynamic output = await plugin.Invoke(input);
    File.WriteAllBytes("test5.pdf", (byte[])output.Result);
    Process.Start("explorer.exe", "test2.pdf");
}

async Task TestHttpPlugin()
{
    var plugin = new Factory.HttpPlugin.HttpPlugin();

    dynamic configuration = new ExpandoObject();
    configuration.BaseUrl = "https://pay.easebuzz.in";
    plugin.Configure(configuration);

    dynamic payload = new ExpandoObject();

    KeyValuePair<string, string>[] formFields = new KeyValuePair<string, string>[]
 {
    new KeyValuePair<string, string>("key", "KYSKKYZMXA"),
    new KeyValuePair<string, string>("txnid", "8409122024044646"),
    new KeyValuePair<string, string>("amount", "82600"),
    new KeyValuePair<string, string>("productinfo", "Membership"),
    new KeyValuePair<string, string>("firstname", "Shashwati Builders"),
    new KeyValuePair<string, string>("phone", "9890455177"),
    new KeyValuePair<string, string>("email", "<EMAIL>"),
    new KeyValuePair<string, string>("surl", "https://credai-pune.justservices.in/mvc/cc9c6b52-a0f6-405c-8270-76f2321d7cd9/65d0adc88672cc5ed0eedc53?txnEntityId=67567636fd27d2124d1d531a&TDS=no"),
    new KeyValuePair<string, string>("furl", "https://credai-pune.justservices.in/mvc/3d187fb1-1c18-48a7-ad9a-71cb9b37a418/65d0adc88672cc5ed0eedc53?txnEntityId=67567636fd27d2124d1d531a&TDS=no"),
    new KeyValuePair<string, string>("hash", "58f385d9930be26fa8d84122ee07f3657fa58aaf902c0b6f9ab0eb939f30cb6e9f42afa216a28e97d7ce9d25b0559fede603870af5db60faeec4ce14e514c9f0"),
 };

    dynamic inputPdf = new ExpandoObject();
    inputPdf.Path = "payment/initiateLink";
    inputPdf.Method = "POST";
    inputPdf.Headers = "Content-Type: application/x-www-form-urlencoded";
    inputPdf.Body = System.Text.Json.JsonSerializer.SerializeToUtf8Bytes(formFields);

    try
    {
        dynamic outputPdf = await plugin.Invoke(inputPdf);
        Console.WriteLine($"Response: {outputPdf}");
    }
    catch (Exception ex)
    {
        // Handle exceptions
        Console.WriteLine($"Error: {ex.Message}");
    }
}
