﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace ServiceReference
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ServiceModel.ServiceContractAttribute(Namespace="http://ds.ws.emas/", ConfigurationName="ServiceReference.DSVerifyWS")]
    public interface DSVerifyWS
    {
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<ServiceReference.listKeysResponse> listKeysAsync(ServiceReference.listKeys request);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<ServiceReference.signPdfResponse> signPdfAsync(ServiceReference.signPdf request);
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="listKeys", WrapperNamespace="http://ds.ws.emas/", IsWrapped=true)]
    public partial class listKeys
    {
        
        public listKeys()
        {
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="listKeysResponse", WrapperNamespace="http://ds.ws.emas/", IsWrapped=true)]
    public partial class listKeysResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://ds.ws.emas/", Order=0)]
        [System.Xml.Serialization.XmlElementAttribute("return", Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public string[] @return;
        
        public listKeysResponse()
        {
        }
        
        public listKeysResponse(string[] @return)
        {
            this.@return = @return;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="signPdf", WrapperNamespace="http://ds.ws.emas/", IsWrapped=true)]
    public partial class signPdf
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://ds.ws.emas/", Order=0)]
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public string arg0;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://ds.ws.emas/", Order=1)]
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public string arg1;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://ds.ws.emas/", Order=2)]
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public string arg2;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://ds.ws.emas/", Order=3)]
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public string arg3;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://ds.ws.emas/", Order=4)]
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public string arg4;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://ds.ws.emas/", Order=5)]
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public string arg5;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://ds.ws.emas/", Order=6)]
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public string arg6;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://ds.ws.emas/", Order=7)]
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public string arg7;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://ds.ws.emas/", Order=8)]
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public string arg8;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://ds.ws.emas/", Order=9)]
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public string arg9;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://ds.ws.emas/", Order=10)]
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public string arg10;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://ds.ws.emas/", Order=11)]
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public string arg11;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://ds.ws.emas/", Order=12)]
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public string arg12;
        
        public signPdf()
        {
        }
        
        public signPdf(string arg0, string arg1, string arg2, string arg3, string arg4, string arg5, string arg6, string arg7, string arg8, string arg9, string arg10, string arg11, string arg12)
        {
            this.arg0 = arg0;
            this.arg1 = arg1;
            this.arg2 = arg2;
            this.arg3 = arg3;
            this.arg4 = arg4;
            this.arg5 = arg5;
            this.arg6 = arg6;
            this.arg7 = arg7;
            this.arg8 = arg8;
            this.arg9 = arg9;
            this.arg10 = arg10;
            this.arg11 = arg11;
            this.arg12 = arg12;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="signPdfResponse", WrapperNamespace="http://ds.ws.emas/", IsWrapped=true)]
    public partial class signPdfResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://ds.ws.emas/", Order=0)]
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public string @return;
        
        public signPdfResponse()
        {
        }
        
        public signPdfResponse(string @return)
        {
            this.@return = @return;
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    public interface DSVerifyWSChannel : ServiceReference.DSVerifyWS, System.ServiceModel.IClientChannel
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    public partial class DSVerifyWSClient : System.ServiceModel.ClientBase<ServiceReference.DSVerifyWS>, ServiceReference.DSVerifyWS
    {
        
        /// <summary>
        /// Implement this partial method to configure the service endpoint.
        /// </summary>
        /// <param name="serviceEndpoint">The endpoint to configure</param>
        /// <param name="clientCredentials">The client credentials</param>
        static partial void ConfigureEndpoint(System.ServiceModel.Description.ServiceEndpoint serviceEndpoint, System.ServiceModel.Description.ClientCredentials clientCredentials);
        
        public DSVerifyWSClient() : 
                base(DSVerifyWSClient.GetDefaultBinding(), DSVerifyWSClient.GetDefaultEndpointAddress())
        {
            this.Endpoint.Name = EndpointConfiguration.DSVerifyWSImplPort.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public DSVerifyWSClient(EndpointConfiguration endpointConfiguration) : 
                base(DSVerifyWSClient.GetBindingForEndpoint(endpointConfiguration), DSVerifyWSClient.GetEndpointAddress(endpointConfiguration))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public DSVerifyWSClient(EndpointConfiguration endpointConfiguration, string remoteAddress) : 
                base(DSVerifyWSClient.GetBindingForEndpoint(endpointConfiguration), new System.ServiceModel.EndpointAddress(remoteAddress))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public DSVerifyWSClient(EndpointConfiguration endpointConfiguration, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(DSVerifyWSClient.GetBindingForEndpoint(endpointConfiguration), remoteAddress)
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public DSVerifyWSClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress)
        {
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<ServiceReference.listKeysResponse> ServiceReference.DSVerifyWS.listKeysAsync(ServiceReference.listKeys request)
        {
            return base.Channel.listKeysAsync(request);
        }
        
        public System.Threading.Tasks.Task<ServiceReference.listKeysResponse> listKeysAsync()
        {
            ServiceReference.listKeys inValue = new ServiceReference.listKeys();
            return ((ServiceReference.DSVerifyWS)(this)).listKeysAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<ServiceReference.signPdfResponse> ServiceReference.DSVerifyWS.signPdfAsync(ServiceReference.signPdf request)
        {
            return base.Channel.signPdfAsync(request);
        }
        
        public System.Threading.Tasks.Task<ServiceReference.signPdfResponse> signPdfAsync(string arg0, string arg1, string arg2, string arg3, string arg4, string arg5, string arg6, string arg7, string arg8, string arg9, string arg10, string arg11, string arg12)
        {
            ServiceReference.signPdf inValue = new ServiceReference.signPdf();
            inValue.arg0 = arg0;
            inValue.arg1 = arg1;
            inValue.arg2 = arg2;
            inValue.arg3 = arg3;
            inValue.arg4 = arg4;
            inValue.arg5 = arg5;
            inValue.arg6 = arg6;
            inValue.arg7 = arg7;
            inValue.arg8 = arg8;
            inValue.arg9 = arg9;
            inValue.arg10 = arg10;
            inValue.arg11 = arg11;
            inValue.arg12 = arg12;
            return ((ServiceReference.DSVerifyWS)(this)).signPdfAsync(inValue);
        }
        
        public virtual System.Threading.Tasks.Task OpenAsync()
        {
            return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginOpen(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndOpen));
        }
        
        private static System.ServiceModel.Channels.Binding GetBindingForEndpoint(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.DSVerifyWSImplPort))
            {
                System.ServiceModel.BasicHttpBinding result = new System.ServiceModel.BasicHttpBinding();
                result.MaxBufferSize = int.MaxValue;
                result.ReaderQuotas = System.Xml.XmlDictionaryReaderQuotas.Max;
                result.MaxReceivedMessageSize = int.MaxValue;
                result.AllowCookies = true;
                return result;
            }
            throw new System.InvalidOperationException(string.Format("Could not find endpoint with name \'{0}\'.", endpointConfiguration));
        }
        
        private static System.ServiceModel.EndpointAddress GetEndpointAddress(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.DSVerifyWSImplPort))
            {
                return new System.ServiceModel.EndpointAddress("http://**************:8080/emSigner/services/dsverifyWS");
            }
            throw new System.InvalidOperationException(string.Format("Could not find endpoint with name \'{0}\'.", endpointConfiguration));
        }
        
        private static System.ServiceModel.Channels.Binding GetDefaultBinding()
        {
            return DSVerifyWSClient.GetBindingForEndpoint(EndpointConfiguration.DSVerifyWSImplPort);
        }
        
        private static System.ServiceModel.EndpointAddress GetDefaultEndpointAddress()
        {
            return DSVerifyWSClient.GetEndpointAddress(EndpointConfiguration.DSVerifyWSImplPort);
        }
        
        public enum EndpointConfiguration
        {
            
            DSVerifyWSImplPort,
        }
    }
}
