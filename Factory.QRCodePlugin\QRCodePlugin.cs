using System.Collections.Generic;
using System.Dynamic;
using System.Net;
using System.Text;
using Factory.Plugins;
using Factory.Models;
using Net.Codecrete.QrCodeGenerator;

namespace Factory.QRCodePlugin {

    public class QRCodePlugin : IRemoteCallPlugin
    {
        private static List<string> Methods = new List<string>() {
            "Generate"
        };

        public string GetName() {
            return "QR Code";
        }

        public List<FieldMeta> GetConfigurationMeta()
        {
            return new List<FieldMeta>() {
            };
        }

        public List<string> GetMethods()
        {
            return Methods;
        }

        public string GetHelp()
        {
            return "";
        }


        public void Configure(dynamic configuration) {
        }

        public async Task<dynamic> Invoke(dynamic input)
        {
            var payload = input.Payload.ToString();
            var qr = QrCode.EncodeBinary(Encoding.UTF8.GetBytes(payload), QrCode.Ecc.Medium);
            dynamic output = new ExpandoObject();
            output.Result = qr.ToSvgString(4);
            return await Task.FromResult(output);
        }
    }
}