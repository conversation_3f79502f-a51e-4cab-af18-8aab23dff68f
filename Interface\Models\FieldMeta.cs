namespace Factory.Models
{
    public enum FieldCategory
    {
        Primitive, Entity, MasterD<PERSON>, User, ValueObject
    }

    public enum PrimitiveType
    {
        Number, Decimal, Boolean, Text, Date, LongLat, File
    }

    public enum StorageType
    {
        Plain, Encrypted, Hashed, Salted
    }

    public class FieldMeta
    {
        public string Title { get; set; }
        public string Name { get; set; }
        public FieldCategory Category { get; set; }
        public string Type { get; set; } // For Primitive (Number, Decimal, Boolean, Text, Date), For Value Object (Entity Id), For Master Data (Master Meta Id)
        public string FieldType { get; set; }
        public StorageType StorageType { get; set; }
        public bool IsCollection { get; set; }
        public int MaxAllowed { get; set; } // For Collection, Text
        public bool IsUnique { get; set; }
        public bool IsRequired { get; set; }
        public String RegExPattern { get; set; } // For Text
        public string MinValue { get; set; } // For Date, Number, Decimal
        public string MaxValue { get; set; } // For Date, Number, Decimal
        public string FileExtension { get; set; }
        public string DateFormat { get; set; }
    }
}