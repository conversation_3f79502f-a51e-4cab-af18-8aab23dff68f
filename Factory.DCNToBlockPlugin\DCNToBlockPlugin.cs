using System.Collections.Generic;
using System.Net;
using System.Text;
using Factory.Plugins;
using Factory.Models;
using System.Dynamic;
using System.Text.Json;
using System.Net.Http.Headers;

namespace Factory.DCNToBlockPlugin {

    public class DCNToBlockPlugin : IRemoteCallPlugin
    {
        public string GetName() {
            return "DCN To Block";
        }

        public List<string> GetMethods()
        {
            return new List<string>() {
                "Generate"
            };
        }

        public string GetHelp()
        {
            return "";
        }

        public List<FieldMeta> GetConfigurationMeta()
        {
            return new List<FieldMeta>() {
            };
        }

        public void Configure(dynamic configuration) {
        }

        public async Task<dynamic> Invoke(dynamic input)
        {
            var templateId = "6365f3ae5d55b182235128db";
            var blockId = input.DCNId;
            using (var httpClient = new HttpClient()) {
                var byteContent = new ByteArrayContent(
                    Encoding.UTF8.GetBytes(
                        JsonSerializer.Serialize(new {
                            BlockId = blockId,
                            TemplateId = templateId,
                            Payload = input
                        })
                    )
                );
                byteContent.Headers.ContentType = new MediaTypeHeaderValue("application/json");
                var result  = await httpClient.PostAsync("https://entityblock.invimatic.com/api/block", byteContent);
                if (!result.IsSuccessStatusCode) 
                {
                    throw new Exception(await result.Content.ReadAsStringAsync());
                }
            }
            dynamic output = new ExpandoObject();
            output.Result = $"https://entityblock.invimatic.com/qr?blockId={blockId}";
            return output;
        }

    }
}