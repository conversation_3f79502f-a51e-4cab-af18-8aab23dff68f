using QuestPDF.Fluent;
using QuestPDF.Infrastructure;
using QuestPDF.Drawing;
using Svg.Skia;
using Factory.Plugins;
using Factory.Models;
using System.Reflection;
using System.Dynamic;
using QuestPDF.Helpers;
using SkiaSharp;
using static Factory.HoadingFormPlugin.Mapper;

namespace Factory.HoadingFormPlugin
{
    public class HoadingFormPlugin : IRemoteCallPlugin
    {
        public string GetName()
        {
            return "HoadingForm";
        }

        public List<string> GetMethods()
        {
            return new List<string>() {
                "Generate"
            };
        }

        public string GetHelp()
        {
            return "";
        }

        public List<FieldMeta> GetConfigurationMeta()
        {
            return new List<FieldMeta>() { };
        }



        public void Configure(dynamic configuration)
        {
        }

        public Task<dynamic> Invoke(dynamic input)
        {

            dynamic output = new ExpandoObject();
            output.Result = new HoadingForm(Mapper.Map<Model>(input)).GeneratePdf();
            return Task.FromResult<dynamic>(output);
        }
    }

    public class HoadingForm : IDocument
    {
        static HoadingForm()
        {
            var mangalFont = Assembly.GetExecutingAssembly().GetManifestResourceStream("Factory.HoadingFormPlugin.Mangal.ttf");
            if (mangalFont != null)
            {
                FontManager.RegisterFont(mangalFont);
            }
            var timesFont = Assembly.GetExecutingAssembly().GetManifestResourceStream("Factory.HoadingFormPlugin.times.ttf");
            if (timesFont != null)
            {
                FontManager.RegisterFont(timesFont);
            }
        }



        public Model _model { get; }

        public HoadingForm(Model model)
        {
            _model = model;
        }


        public DocumentMetadata GetMetadata() => DocumentMetadata.Default;

        public void Compose(IDocumentContainer container)
        {
            container
                .Page(page =>
                {
                    page.Margin(30);
                    // page.Header().Element(ComposeHeader);
                    page.Content().Element(ComposeContent);


                });
        }
        void ComposeContent(IContainer container)
        {
            container.Column(column =>
            {
                column.Item()
            .AlignRight()
            .Text(text =>
            {

                text.Span(_model.ApplicationId).FontSize(12)
                    .FontFamily("Times New Roman").Bold();
            });
                var stream = Assembly.GetExecutingAssembly().GetManifestResourceStream("Factory.HoadingFormPlugin.Logo.png");
                if (stream != null)
                {

                    column.Item().Row(row =>
                    {
                        row.RelativeItem(4);
                        row.RelativeItem(1)
                            .AlignCenter()
                            .Height(50)
                            .Image(new BinaryReader(stream).ReadBytes((int)stream.Length), ImageScaling.Resize);
                        row.RelativeItem(4);
                    });
                }
                column.Item()
                    .AlignCenter()
                    .Text(text =>
                    {

                        text.Span("पुणे महानगरपालिका").FontSize(12)
                            .FontFamily("Mangal").Bold();
                    });
                column.Item()
               .AlignCenter()
               .Text(text =>
               {

                   text.Span("उप आयुक्त, परवाना व आकाश चिन्ह कार्यालय").FontSize(11)
                       .FontFamily("Mangal").Bold();
               });
                column.Item()
              .AlignCenter()
              .Text(text =>
              {

                  text.Span("(महाराष्ट्र शासन राजपत्र असाधारण,जून १९, २००३ /ज्येष्ठ २९, शके १९२५)").FontSize(7)
                      .FontFamily("Mangal");
              });
                column.Item()
             .AlignCenter()
             .Text(text =>
             {

                 text.Span("(मुंबई प्रांतिक महानगरपालिका अधिनियम, १९४९ चे कलम २४४ तथा नियमाप्रमाणे)").FontSize(7)
                     .FontFamily("Mangal")
                     ;
             });


                column.Item()
                .PaddingTop(10)
               .AlignCenter()
               .Text(text =>
               {

                   text.Span("आकाश चिन्ह उभारण्याबाबत परवाना मागणी अर्ज").FontSize(10)
                       .FontFamily("Mangal").Bold()
                       ;
               });
                //  var highlight = TextStyle.Default.BackgroundColor(Colors.Green.Lighten3);
                column.Item().Grid(grid =>
                {
                    grid.AlignLeft();
                    grid.Columns(10);
                    grid.Item(10).Background(Colors.Grey.Lighten3).Height(35)
                        .PaddingTop(10)
                     .Text(text =>
                     {

                         text.Span("कार्यालयीन उपयोगासाठी").FontSize(10)
                             .FontFamily("Mangal").Bold();


                     })
                    ;
                });
                //     column.Item()
                //     .PaddingTop(10)
                //  .Text(text =>
                //  {

                //      text.Span("कार्यालयीन उपयोगासाठी").Style(highlight).FontSize(10)
                //          .FontFamily("Mangal").Bold()
                //          ;
                //  });
                column.Item()
                  .MinimalBox()
                 .Border(1)

                         .Table(table =>
                         {
                             IContainer DefaultCellStyle(IContainer cellContainer, string backgroundColor)
                             {
                                 return cellContainer
                                     .Border(1)
                                  .PaddingVertical(10)
                                   .PaddingHorizontal(10);
                                 //  .AlignCenter()
                                 //  .AlignMiddle();
                             }

                             table.ColumnsDefinition(columns =>
                             {
                                 columns.RelativeColumn(1);
                                 columns.RelativeColumn(1);
                             });

                             table.Header(header =>
                             {
                                 header.Cell().RowSpan(2).Element(CellStyle).AlignCenter().Text("स्वीकृती").FontFamily("Mangal")
                                       .FontSize(10).Bold();
                                 header.Cell().RowSpan(2).Element(CellStyle).AlignCenter().Text(" वितरण").FontFamily("Mangal").FontSize(10).Bold();
                                 IContainer CellStyle(IContainer cellContainer) => DefaultCellStyle(cellContainer, Colors.Grey.Lighten3);
                             });
                             table.Cell().RowSpan(1).Element(CellStyle).AlignLeft().Text(text =>
                             {
                                 text.DefaultTextStyle(x => x.FontSize(9).FontFamily("Mangal").LineHeight(1));
                                 text.Span($"स्वीकृती क्षेत्रीय कार्यालयाचे   :").FontFamily("Mangal").FontSize(9);
                                 text.Line($"").FontFamily("Times New Roman").FontSize(9);
                                 text.Span($"आवक क्र. व दिनांक  :").FontFamily("Mangal").FontSize(9);
                                 text.Line($":").FontFamily("Times New Roman").FontSize(9);
                                 text.Span($"(प्रोसेसिंग चार्ज पावतीवरील)  :").FontFamily("Mangal").FontSize(9);
                                 text.Line($"").FontFamily("Times New Roman").FontSize(9);
                                 text.Span($"प्रोसेसिंग चार्जेस रुपये :").FontFamily("Mangal").FontSize(9);
                                 text.Line($"").FontFamily("Times New Roman").FontSize(9);
                                 text.Span($"व्यवसायाचा प्रकार  :").FontFamily("Mangal").FontSize(9);
                                 text.Line($"").FontFamily("Times New Roman").FontSize(9);
                                 text.Span($"परवाना निरीक्षक :").FontFamily("Mangal").FontSize(9);
                                 text.Line($"").FontFamily("Times New Roman").FontSize(9);
                                 //  text.DefaultTextStyle(x => x.FontSize(9).FontFamily("Mangal").LineHeight(1));
                                 //  text.Span($"स्वीकृती षेत्रीय कार्यालयाचे   :").FontFamily("Mangal").FontSize(9);
                                 //  text.Line($"Of the Office of Acceptance:").FontFamily("Times New Roman").FontSize(9);
                                 //  text.Span($"आवक क्र. व दिनांक  :").FontFamily("Mangal").FontSize(9);
                                 //  text.Line($"in inflow No. and date:").FontFamily("Times New Roman").FontSize(9);
                                 //  text.Span($"(प्रोसेसिंग चार्ज पावतीवरील)  :").FontFamily("Mangal").FontSize(9);
                                 //  text.Line($"(On processing charge receipt):").FontFamily("Times New Roman").FontSize(9);
                                 //  text.Span($"प्रोसेसिंग चार्जेस रुपये :").FontFamily("Mangal").FontSize(9);
                                 //  text.Line($"Processing charges Rs").FontFamily("Times New Roman").FontSize(9);
                                 //  text.Span($"व्यवसायाचा प्रकार  :").FontFamily("Mangal").FontSize(9);
                                 //  text.Line($"Type of business:").FontFamily("Times New Roman").FontSize(9);
                                 //  text.Span($"परवाना निरीक्षक :").FontFamily("Mangal").FontSize(9);
                                 //  text.Line($"License Inspector:").FontFamily("Times New Roman").FontSize(9);

                             });
                             table.Cell().RowSpan(1).Element(CellStyle).AlignLeft().Text(text =>
                            {
                                text.DefaultTextStyle(x => x.FontSize(9).FontFamily("Mangal").LineHeight(1));
                                text.Span($"परवाना फी पावती क्र., दिनांक:").FontFamily("Mangal").FontSize(9);
                                text.Line($"").FontFamily("Times New Roman").FontSize(9);
                                text.Span($"परवाना कालावधी:").FontFamily("Mangal").FontSize(9);
                                text.Line($"").FontFamily("Times New Roman").FontSize(9);
                                text.Span($"परवाना मान्यता दिनांक:").FontFamily("Mangal").FontSize(9);
                                text.Line($"").FontFamily("Times New Roman").FontSize(9);
                                text.Span($"").FontFamily("Mangal").FontSize(9);
                                text.Line($"").FontFamily("Times New Roman").FontSize(9);
                                text.Span($"परवाना मिळाल्याची स्वाक्षरी:").FontFamily("Mangal").FontSize(9);
                                text.Line($"").FontFamily("Times New Roman").FontSize(9);
                                text.Span($"व नाव:").FontFamily("Mangal").FontSize(9);
                                text.Line($"").FontFamily("Times New Roman").FontSize(9);

                            });


                             IContainer CellStyle(IContainer cellContainer) => DefaultCellStyle(cellContainer, Colors.White).ShowOnce();
                         });
                column.Item().PaddingTop(50).Grid(grid =>
      {
          grid.AlignLeft();
          grid.Columns(10);
          grid.Item(10).Background(Colors.Grey.Lighten3).Height(35)
              .PaddingTop(10)
           .Text(text =>
           {

               text.Span("अर्जदाराची माहिती:").FontSize(10)
                   .FontFamily("Mangal").Bold();


           })
          ;
      });
                //         column.Item()
                //     .PaddingTop(10)
                //     .Text(text =>
                //     {

                //         text.Span("अर्जदाराची माहिती: ").FontSize(10)
                // .FontFamily("Mangal").Bold()
                // ;
                //     });

                column.Item()
            .MinimalBox()
            .Border(1)

               .Table(table =>
               {
                   IContainer DefaultCellStyle(IContainer cellContainer, string backgroundColor)
                   {
                       return cellContainer
                           .Border(1)
                        .PaddingVertical(10)
                         .PaddingHorizontal(10);

                   }

                   table.ColumnsDefinition(columns =>
                   {
                       columns.RelativeColumn(2);
                   });
                   table.ColumnsDefinition(columns =>
            {
                columns.ConstantColumn(150);
                columns.ConstantColumn(75);
                columns.ConstantColumn(75);
                columns.ConstantColumn(75);
                columns.ConstantColumn(75);
                columns.RelativeColumn();
            });
                   table.Header(header =>
                       {


                           header.Cell().RowSpan(2).Element(CellStyle).AlignCenter().PaddingTop(13).Text("अर्जदाराचे नाव:").FontFamily("Mangal").FontSize(10).Bold();
                           header.Cell().ColumnSpan(1).Element(CellStyle).Text("आडनाव").FontFamily("Mangal").FontSize(9);
                           header.Cell().ColumnSpan(1).Element(CellStyle).Text("नाव").FontFamily("Mangal").FontSize(9);
                           header.Cell().ColumnSpan(1).Element(CellStyle).Text("वडील/पतीचे नाव").FontFamily("Mangal").FontSize(9);
                           header.Cell().ColumnSpan(1).Element(CellStyle).Text("लिंग").FontFamily("Mangal").FontSize(9);
                           header.Cell().ColumnSpan(1).Element(CellStyle).Text("वय").FontFamily("Mangal").FontSize(9);

                           header.Cell().Element(CellStyle).Text(_model.Surname).FontSize(9);
                           header.Cell().Element(CellStyle).Text(_model.FirstName).FontSize(9);
                           header.Cell().Element(CellStyle).Text(_model.FatherName).FontSize(9);
                           header.Cell().Element(CellStyle).Text(_model.Gender).FontSize(9);
                           header.Cell().Element(CellStyle).Text(_model.Age).FontSize(9);

                           IContainer CellStyle(IContainer container) => DefaultCellStyle(container, Colors.Grey.Lighten3);
                       });

                   IContainer CellStyle(IContainer cellContainer) => DefaultCellStyle(cellContainer, Colors.White).ShowOnce();
               });
                column.Item()
                       .MinimalBox()
                      .Border(1)

                              .Table(table =>
                              {
                                  IContainer DefaultCellStyle(IContainer cellContainer, string backgroundColor)
                                  {
                                      return cellContainer
                                          .Border(1)
                                       .PaddingVertical(10)
                                        .PaddingHorizontal(10);

                                  }

                                  table.ColumnsDefinition(columns =>
                                  {
                                      columns.RelativeColumn(2);
                                  });
                                  table.ColumnsDefinition(columns =>
               {
                   columns.ConstantColumn(150);
                   columns.RelativeColumn();
               });
                                  table.Header(header =>
                                      {
                                          header.Cell().RowSpan(1).Element(CellStyle).AlignCenter().PaddingTop(13).Text("राहण्याचा पत्ता:").FontFamily("Mangal").FontSize(10).Bold();
                                          header.Cell().Element(CellStyle).Text(_model.Address).FontSize(9);
                                          IContainer CellStyle(IContainer container) => DefaultCellStyle(container, Colors.Grey.Lighten3);
                                      });
                                  IContainer CellStyle(IContainer cellContainer) => DefaultCellStyle(cellContainer, Colors.White).ShowOnce();
                              });

                column.Item()
                .MinimalBox()
               .Border(1)

                       .Table(table =>
                       {
                           IContainer DefaultCellStyle(IContainer cellContainer, string backgroundColor)
                           {
                               return cellContainer
                                   .Border(1)
                                .PaddingVertical(10)
                                 .PaddingHorizontal(10);

                           }

                           //    table.ColumnsDefinition(columns =>
                           //    {
                           //        columns.RelativeColumn(2);
                           //    });
                           table.ColumnsDefinition(columns =>
            {
                columns.ConstantColumn(150);
                columns.ConstantColumn(150);
                columns.RelativeColumn();
            });
                           table.Header(header =>
                               {
                                   header.Cell().ColumnSpan(1).Element(CellStyle).Text("फोन नं.").FontFamily("Mangal").FontSize(9);
                                   header.Cell().ColumnSpan(1).Element(CellStyle).Text("मोबाईल नं.:").FontFamily("Mangal").FontSize(9);
                                   header.Cell().ColumnSpan(1).Element(CellStyle).Text("ई-मेल:").FontFamily("Mangal").FontSize(9);
                                   header.Cell().Element(CellStyle).Text(_model.PhoneNumber).FontSize(9);
                                   header.Cell().Element(CellStyle).Text(_model.Mobilenumber).FontSize(9);
                                   header.Cell().Element(CellStyle).Text(_model.Email).FontSize(9);
                                   IContainer CellStyle(IContainer container) => DefaultCellStyle(container, Colors.Grey.Lighten3);
                               });
                           IContainer CellStyle(IContainer cellContainer) => DefaultCellStyle(cellContainer, Colors.White).ShowOnce();
                       });



                column.Item().PageBreak();
                //Bussiness Information
                column.Item().PaddingTop(50).Grid(grid =>
             {
                 grid.AlignLeft();
                 grid.Columns(10);
                 grid.Item(10).Background(Colors.Grey.Lighten3).Height(35)
                     .PaddingTop(10)
                  .Text(text =>
                  {

                      text.Span("व्यवसायासंबंधी माहिती:").FontSize(10)
                          .FontFamily("Mangal").Bold();


                  })
                 ;
             });

                column.Item()
                                      .MinimalBox()
                                     .Border(1)

                                             .Table(table =>
                                             {
                                                 IContainer DefaultCellStyle(IContainer cellContainer, string backgroundColor)
                                                 {
                                                     return cellContainer
                                                         .Border(1)
                                                      .PaddingVertical(10)
                                                       .PaddingHorizontal(10);

                                                 }

                                                 //  table.ColumnsDefinition(columns =>
                                                 //  {
                                                 //      columns.RelativeColumn(2);
                                                 //  });
                                                 table.ColumnsDefinition(columns =>
                              {
                                  columns.ConstantColumn(130);
                                  columns.RelativeColumn();
                              });
                                                 table.Header(header =>
                                                     {
                                                         header.Cell().RowSpan(2).Element(CellStyle).AlignCenter().PaddingTop(13).Text("व्यवसायाचा प्रकार:").FontFamily("Mangal").FontSize(10).Bold();
                                                         header.Cell().RowSpan(2).Element(CellStyle).AlignCenter().PaddingTop(13).Text(_model.TypeofBusiness).FontFamily("Times New Roman").FontSize(9).Bold();
                                                         header.Cell().Element(CellStyle).Text("फर्मचे/ दुकानाचे नाव:").FontFamily("Mangal").FontSize(9);
                                                         header.Cell().Element(CellStyle).Text(_model.FirmName).FontFamily("Times New Roman").FontSize(9);
                                                         header.Cell().Element(CellStyle).Text("आकाशचिन्ह उभारण्याचे ठिकाण पेठ :").FontFamily("Mangal").FontSize(9);
                                                         header.Cell().Element(CellStyle).Text(_model.Peth).FontFamily("Times New Roman").FontSize(9);
                                                         header.Cell().ColumnSpan(1).Element(CellStyle).Text(" घरांक :").FontFamily("Mangal").FontSize(9);
                                                         header.Cell().ColumnSpan(1).Element(CellStyle).Text($"{_model.HouseNo}").FontFamily("Times New Roman").FontSize(9);
                                                         header.Cell().ColumnSpan(1).Element(CellStyle).Text("सर्वे नं. :").FontFamily("Mangal").FontSize(9);
                                                         header.Cell().Element(CellStyle).Text($"{_model.SurveyNo}").FontFamily("Times New Roman").FontSize(9);
                                                         header.Cell().Element(CellStyle).Text("प्लॉ. नं.").FontFamily("Mangal").FontSize(9);
                                                         header.Cell().Element(CellStyle).Text($"{_model.PlotNo}").FontFamily("Times New Roman").FontSize(9);
                                                         header.Cell().Element(CellStyle).Text("नामफलक, बॅनर दिशादर्शक फलक:").FontFamily("Mangal").FontSize(9);
                                                         header.Cell().Element(CellStyle).Text($"{_model.NameBoard}").FontFamily("Times New Roman").FontSize(9);
                                                         header.Cell().Element(CellStyle).Text("चौकाचे नाव :").FontFamily("Mangal").FontSize(9);
                                                         header.Cell().Element(CellStyle).Text($"{_model.NameOfChowk}").FontFamily("Times New Roman").FontSize(9);
                                                         header.Cell().Element(CellStyle).Text(" दिशादर्शक:  ").FontFamily("Mangal").FontSize(9);
                                                         header.Cell().Element(CellStyle).Text($"{_model.StreetName}").FontFamily("Times New Roman").FontSize(9);
                                                         header.Cell().Element(CellStyle).Text("जवळची खुण :").FontFamily("Mangal").FontSize(9);
                                                         header.Cell().Element(CellStyle).Text($"{_model.LandMark}").FontFamily("Times New Roman").FontSize(9);


                                                         IContainer CellStyle(IContainer container) => DefaultCellStyle(container, Colors.Grey.Lighten3);
                                                     });
                                                 IContainer CellStyle(IContainer cellContainer) => DefaultCellStyle(cellContainer, Colors.White).ShowOnce();
                                             });
                column.Item().PaddingTop(50).Grid(grid =>
                           {
                               grid.AlignLeft();
                               grid.Columns(10);
                               grid.Item(10).Background(Colors.Grey.Lighten3).Height(35)
                                   .PaddingTop(10)
                                .Text(text =>
                                {

                                    text.Span("भागीदारांबाबत माहिती").FontSize(10)
                                        .FontFamily("Mangal").Bold();


                                })
                               ;
                           });

                column.Item()
                              .MinimalBox()
                             .Border(1)

                                     .Table(table =>
                                     {
                                         IContainer DefaultCellStyle(IContainer cellContainer, string backgroundColor)
                                         {
                                             return cellContainer
                                                 .Border(1)
                                              .PaddingVertical(10)
                                               .PaddingHorizontal(10);

                                         }

                                         //    table.ColumnsDefinition(columns =>
                                         //    {
                                         //        columns.RelativeColumn(2);
                                         //    });
                                         table.ColumnsDefinition(columns =>
                          {
                              columns.ConstantColumn(40);
                              columns.ConstantColumn(150);
                              columns.ConstantColumn(70);
                              columns.ConstantColumn(70);
                              columns.ConstantColumn(70);
                              columns.RelativeColumn();
                          });
                                         table.Header(header =>
                                             {
                                                 header.Cell().ColumnSpan(1).Element(CellStyle).Text("अ.क्र.").FontFamily("Mangal").FontSize(9);
                                                 header.Cell().ColumnSpan(1).Element(CellStyle).Text("भागीदाराचे कंपनीचे नाव, पत्ता ").FontFamily("Mangal").FontSize(9);
                                                 header.Cell().ColumnSpan(1).Element(CellStyle).Text("पत्ता").FontFamily("Mangal").FontSize(9);
                                                 header.Cell().ColumnSpan(1).Element(CellStyle).Text("लिंग").FontFamily("Mangal").FontSize(9);
                                                 header.Cell().ColumnSpan(1).Element(CellStyle).Text("वय").FontFamily("Mangal").FontSize(9);
                                                 header.Cell().ColumnSpan(1).Element(CellStyle).Text("व्यवसाय").FontFamily("Mangal").FontSize(9);
                                                 header.Cell().Element(CellStyle).Text("").FontSize(9);
                                                 header.Cell().Element(CellStyle).Text("").FontSize(9);
                                                 header.Cell().Element(CellStyle).Text("").FontSize(9);
                                                 header.Cell().Element(CellStyle).Text("").FontSize(9);
                                                 header.Cell().Element(CellStyle).Text("").FontSize(9);
                                                 header.Cell().Element(CellStyle).Text("").FontSize(9);
                                                 header.Cell().Element(CellStyle).Text("").FontSize(9);
                                                 header.Cell().Element(CellStyle).Text("").FontSize(9);
                                                 header.Cell().Element(CellStyle).Text("").FontSize(9);
                                                 header.Cell().Element(CellStyle).Text("").FontSize(9);
                                                 header.Cell().Element(CellStyle).Text("").FontSize(9);
                                                 header.Cell().Element(CellStyle).Text("").FontSize(9);
                                                 header.Cell().Element(CellStyle).Text("").FontSize(9);
                                                 header.Cell().Element(CellStyle).Text("").FontSize(9);
                                                 header.Cell().Element(CellStyle).Text("").FontSize(9);
                                                 header.Cell().Element(CellStyle).Text("").FontSize(9);
                                                 header.Cell().Element(CellStyle).Text("").FontSize(9);
                                                 header.Cell().Element(CellStyle).Text("").FontSize(9);
                                                 IContainer CellStyle(IContainer container) => DefaultCellStyle(container, Colors.Grey.Lighten3);
                                             });
                                         IContainer CellStyle(IContainer cellContainer) => DefaultCellStyle(cellContainer, Colors.White).ShowOnce();
                                     });

                column.Item().PageBreak();

                column.Item()
                                                     .MinimalBox()
                                                    .Border(1)

                                                            .Table(table =>
                                                            {
                                                                IContainer DefaultCellStyle(IContainer cellContainer, string backgroundColor)
                                                                {
                                                                    return cellContainer
                                                                        .Border(1)
                                                                     .PaddingVertical(4)
                                                                      .PaddingHorizontal(4);

                                                                }

                                                                //  table.ColumnsDefinition(columns =>
                                                                //  {
                                                                //      columns.RelativeColumn(2);
                                                                //  });
                                                                table.ColumnsDefinition(columns =>
                                             {
                                                 columns.RelativeColumn();
                                                 columns.ConstantColumn(175);
                                             });
                                                                table.Header(header =>
                                                                    {
                                                                        header.Cell().RowSpan(2).Element(CellStyle).AlignLeft().Text("(१) आकाश चिन्ह लावण्याच्या ठिकाणी धंदा असल्यास कोणता धंदा खाजगी बोर्ड असल्यास").FontFamily("Mangal").FontSize(9);
                                                                        // column.Item().Height(50);
                                                                        header.Cell().RowSpan(2).Element(CellStyle).AlignLeft().Text(_model.InstallationPrivateOrBusiness).FontFamily("Times New Roman").FontSize(9);
                                                                        header.Cell().Element(CellStyle).Text("(२) आकाश चिन्ह प्रकार प्रकाशित/अप्रकाशित").FontFamily("Mangal").FontSize(9);
                                                                        header.Cell().Element(CellStyle).Text(_model.TypeofHoarding).FontFamily("Times New Roman").FontSize(9);
                                                                        header.Cell().Element(CellStyle).Text(text =>
                                                                        {

                                                                            text.Line("(३) आकाश चिन्हाचे मोजमाप")
                                                                           .FontFamily("Mangal")
                                                                       .FontSize(9);
                                                                            //         text.Line("(क) ३०.५ मीटर (१०  फूट)* ६.१० मीटर (२०  फूट)")
                                                                            //         .FontFamily("Mangal")
                                                                            //     .FontSize(9);
                                                                            //         text.Line("(ख ६.१० मीटर (२०  फूट) * ६.१०  मीटर (२०  फूट )")
                                                                            //         .FontFamily("Mangal")
                                                                            //     .FontSize(9);
                                                                            //         text.Line("(ग) ९.१५ मीटर (30 फूट) * ४.५७५ मीटर (१५ फूट) ")
                                                                            //     .FontFamily("Mangal")
                                                                            // .FontSize(9);
                                                                            //         text.Line("(घ) ९.१५ मीटर (30) फूट *  ६.१० मीटर (२०  फूट) ")
                                                                            //                .FontFamily("Mangal")
                                                                            //            .FontSize(9);
                                                                            //         text.Line("(ड) १२.२ मीटर (४० फूट *  ३०.५ मीटर (१०  फूट) ")
                                                                            //         .FontFamily("Mangal")
                                                                            //     .FontSize(9);
                                                                            //         text.Line("(च) १२.२ मीटर (४०  फूट) *  ६.१० मीटर (२०  फूट)")
                                                                            //     .FontFamily("Mangal")
                                                                            // .FontSize(9);
                                                                        });
                                                                        header.Cell().ColumnSpan(1).Element(CellStyle).Text(_model.Dimension).FontFamily("Times New Roman").FontSize(9);
                                                                        header.Cell().ColumnSpan(1).Element(CellStyle).AlignLeft().Text(@"(३अ) टेरेस वरील फलक  :-  १८.३० मीटर( ६०फूट* ६. १० मीटर (२० फूट)) ").FontFamily("Mangal").FontSize(9);
                                                                        header.Cell().ColumnSpan(1).Element(CellStyle).Text("").FontFamily("Times New Roman").FontSize(9);
                                                                        header.Cell().ColumnSpan(1).Element(CellStyle).Text(text =>
                               {
                                   text.Line("( ४ ) आकाशचिन्ह जमिनीपासून किती उंचीवर लावायचे आहे ?")
                                   .FontFamily("Mangal")
                               .FontSize(9);
                                   text.Line("(अ)जाहिरात फलकाची जमिनीपासून तळ भागाची उंची")
                                   .FontFamily("Mangal")
                               .FontSize(9);
                                   text.Line("(ब)जाहिरात फलकाची रस्त्याचे पातळी पासूनची वरील उंची")
                               .FontFamily("Mangal")
                           .FontSize(9);
                               });
                                                                        header.Cell().ColumnSpan(1).Element(CellStyle).Text(text =>
                                                                        {
                                                                            text.Line(@$"")
                                                                               .FontFamily("Times New Roman")
                                                                                 .FontSize(9);
                                                                            text.Line(@$"")
                                                                          .FontFamily("Times New Roman")
                                                                            .FontSize(9);
                                                                            text.Line(@$"{_model.HeightFromGroundLevel}")
                                                                               .FontFamily("Times New Roman")
                                                                                 .FontSize(9);
                                                                            text.Line(@$"{_model.HeightFromStreetLevel}")
                                                                       .FontFamily("Times New Roman")
                                                                         .FontSize(9);
                                                                        });
                                                                        //  {_model.HeightFromStreetLevel}").FontFamily("Times New Roman").FontSize(9);
                                                                        //   header.Cell().Element(CellStyle).Text("").FontFamily("Times New Roman").FontSize(9);
                                                                        header.Cell().Element(CellStyle).Text("( ५ ) लावण्यात यावयाचे आकाश चिन्ह अर्जदाराच्या स्वतःतर्फे आहे, की इतर दुसऱ्या कंपनीतर्फे आहे ? तसे असल्यास कंपनीचे किंवा व्यक्तीचे संपूर्ण नाव व पत्ता:").FontFamily("Mangal").FontSize(9);
                                                                        header.Cell().Element(CellStyle).Text($" {_model.HoardingInstallation} Name:{_model.InstallationApplicantOrCompanyName}, Address: {_model.AddressofHoardingInstallationpersonorcompany}").FontFamily("Times New Roman").FontSize(9);
                                                                        header.Cell().Element(CellStyle).Text(" ( ६ ) आकाशचिन्ह ज्या ठिकाणी लावायचे आहे ती जागा अर्जदाराच्या स्वतःच्या मालकीची आहे किंवा दुसऱ्याच्या मालकीची आहे ?").FontFamily("Mangal").FontSize(9);
                                                                        header.Cell().Element(CellStyle).Text($"{_model.LandWhereHoardingisbeingInstalled}").FontFamily("Times New Roman").FontSize(9);
                                                                        header.Cell().Element(CellStyle).Text("( ७ ) जागा दुसऱ्याच्या मालकीची असल्यास त्या मालकाचे संपूर्ण नाव व पत्ता :").FontFamily("Mangal").FontSize(9);
                                                                        header.Cell().Element(CellStyle).Text($"Name : {_model.LandOwnerName} Address : {_model.LandOwnerAddress}").FontFamily("Times New Roman").FontSize(9);
                                                                        header.Cell().Element(CellStyle).Text(@"(८) आकाश चिन्ह लावण्यास जागेच्या मालकाची संमती आहे  काय ?
(असल्यास मूळ संमतीपत्र / प्रॉपर्टी कार्ड / ७/१२ उतारा / प्रॉपर्टी टॅक्सचे ना हरकत दाखले जोडावेत)").FontFamily("Mangal").FontSize(9);
                                                                        header.Cell().Element(CellStyle).Text(_model.ConsentOfOwner).FontFamily("Times New Roman").FontSize(9);
                                                                        header.Cell().Element(CellStyle).Text("(९)आकाश चिन्ह लावण्याची ठिकाण मनुष्यवस्थीत आहे किंवा खुल्या जागेत आहे ?").FontFamily("Mangal").FontSize(9);
                                                                        header.Cell().Element(CellStyle).Text(_model.Locationofthehoardinginopenspaceorhumanhabitat).FontFamily("Times New Roman").FontSize(9);
                                                                        header.Cell().Element(CellStyle).Text("(१०) आकाश चिन्ह कोणत्या तारखेपासून लावायचे आहे ?").FontFamily("Mangal").FontSize(9);
                                                                        header.Cell().Element(CellStyle).Text(_model.StartingDateOfInstallationOfHoarding).FontFamily("Times New Roman").FontSize(9);
                                                                        header.Cell().Element(CellStyle).Text("(११)आकाश चिन्ह किती मुदतीपर्यंत लावायचे आहे ?").FontFamily("Mangal").FontSize(9);
                                                                        header.Cell().Element(CellStyle).Text(_model.DurationOfHoarding).FontFamily("Times New Roman").FontSize(9);


                                                                        IContainer CellStyle(IContainer container) => DefaultCellStyle(container, Colors.Grey.Lighten3);
                                                                    });
                                                                IContainer CellStyle(IContainer cellContainer) => DefaultCellStyle(cellContainer, Colors.White).ShowOnce();
                                                            });

                column.Item()
                               .AlignCenter()
                               .Text(text =>
                               {

                                   text.Span("प्रतिज्ञापत्र").FontSize(10)
                                       .FontFamily("Mangal")
                                       .FontSize(15).Bold();
                               });
                column.Item().PaddingTop(20).Text(text =>
                              {
                                  text.Span($" या अर्जाद्वारे आम्ही मागणी केलेली जाहिरात फलकाची परवानगी ही जाहिरात फलक नियंत्रण नियमावली, 2003 च्या धोरणास अनुसरून आहे. या जाहिरात धोरणाचे उल्लंघन झाल्यास सदरची परवानगी केव्हाही रद्द केली जाईल याची मला जाणीव आहे व वरील माहिती माझ्या माहिती व ज्ञानाप्रमाणे सत्य आहे. ती खोटी आढळल्यास माझ्या जाहिरात फलकाची परवानगी रद्द होईल हे मला मान्य आहे.")
                                         .FontFamily("Mangal")
                                      .FontSize(10);
                              });
                column.Item().Row(row =>
               {
                   row.RelativeItem(1)
                           .AlignRight().Text(text =>
                           {
                               text.Span("अर्जदाराची स्वाक्षरी")
                               .FontFamily("Mangal")
                           .FontSize(10);
                           });
               });












            });
            // container.Column(column =>
            // {
            //     column.Item().PaddingTop(50).Text(text =>
            //    {
            //        text.Span($" या अर्जाद्वारे आम्ही मागणी केलेली जाहिरात फलकाची परवानगी ही जाहिरात फलक नियंत्रण नियमावली, 2003 च्या धोरणास अनुसरून आहे. या जाहिरात धोरणाचे उल्लंघन झाल्यास सदरची परवानगी केव्हाही रद्द केली जाईल याची मला जाणीव आहे व वरील माहिती माझ्या माहिती व ज्ञानाप्रमाणे सत्य आहे. ती खोटी आढळल्यास माझ्या जाहिरात बालकाची परवानगी रद्द होईल हे मला मान्य आहे.")
            //               .FontFamily("Mangal")
            //            .FontSize(10);
            //    });
            //     column.Item().Row(row =>
            //    {
            //        row.RelativeItem(1)
            //                .AlignRight().Text(text =>
            //                {
            //                    text.Span("अर्जदाराची स्वाक्षरी")
            //                    .FontFamily("Mangal")
            //                .FontSize(10);
            //                });
            //    });
            // });
        }
    }

    public static class Mapper
    {
        public static T Map<T>(ExpandoObject source) where T : class
        {
            T destination = (T)Activator.CreateInstance(typeof(T));
            IDictionary<string, object> dict = source;
            var type = destination.GetType();

            foreach (var prop in type.GetProperties())
            {
                var lower = prop.Name.ToLower();
                Console.WriteLine($"Mapping {lower}");
                var key = dict.Keys.SingleOrDefault(k => k.ToLower() == lower);

                if (key != null)
                {
                    prop.SetValue(destination, CastProperty(dict[key], prop.PropertyType.ToString()), null);
                }
            }
            return destination;
        }

        static dynamic CastProperty(dynamic property, string type)
        {
            switch (type)
            {
                case "System.String":
                    return (String)property;
                case "System.Decimal":
                    return (Decimal)property;
                default:
                    return property;

            }
        }
        public class Model
        {
            public string ApplicationId { get; set; }
            public string FirstName { get; set; }
            public string FatherName { get; set; }
            public string Surname { get; set; }
            public string Mobilenumber { get; set; }
            public string PhoneNumber { get; set; }
            public string Age { get; set; }
            public string Email { get; set; }
            public string Gender { get; set; }
            public string Address { get; set; }
            public string TypeofBusiness { get; set; }
            public string BusinessRelatedAddress { get; set; }
            public string FirmName { get; set; }
            public string Peth { get; set; }
            public string StreetName { get; set; }
            public string Dimension { get; set; }
            public string HouseNo { get; set; }
            public string SurveyNo { get; set; }
            public string PlotNo { get; set; }
            public string NameBoard { get; set; }
            public string NameOfChowk { get; set; }
            public string Guidelines { get; set; }
            public string LandMark { get; set; }
            public string Measurement { get; set; }
            public string HeightFromGroundLevel { get; set; }
            public string HeightFromStreetLevel { get; set; }
            public string HoardingAddressInformation { get; set; }
            public string InstallationApplicantOrCompanyName { get; set; }
            public string HoardingInstallation { get; set; }
            public string AddressofHoardingInstallationpersonorcompany { get; set; }
            public string LandWhereHoardingisbeingInstalled { get; set; }
            public string LandOwnedByAppicantorSomeone { get; set; }
            public string LandOwnerName { get; set; }
            public string LandOwnerAddress { get; set; }
            public string ConsentOfOwner { get; set; }
            public string DurationOfHoarding { get; set; }
            public string AdditionalDocuments { get; set; }
            public string TypeofHoarding { get; set; }
            public string DetailsoftheApplicantOrCompany { get; set; }
            public string Locationofthehoardinginopenspaceorhumanhabitat { get; set; }
            public string StartingDateOfInstallationOfHoarding { get; set; }
            public string DetailstheApplicantwhoownstheland { get; set; }
            public string InstallationPrivateOrBusiness { get; set; }
        }


    }
}
