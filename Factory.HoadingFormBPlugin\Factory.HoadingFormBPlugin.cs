using QuestPDF.Fluent;
using QuestPDF.Infrastructure;
using QuestPDF.Drawing;
using Svg.Skia;
using Factory.Plugins;
using Factory.Models;
using System.Reflection;
using System.Dynamic;
using QuestPDF.Helpers;
using SkiaSharp;
using static Factory.HoadingFormBPlugin.Mapper;

namespace Factory.HoadingFormBPlugin
{
    public class HoadingFormBPlugin : IRemoteCallPlugin
    {
        public string GetName()
        {
            return "HoadingFormB";
        }

        public List<string> GetMethods()
        {
            return new List<string>() {
                "Generate"
            };
        }

        public string GetHelp()
        {
            return "";
        }

        public List<FieldMeta> GetConfigurationMeta()
        {
            return new List<FieldMeta>() { };
        }



        public void Configure(dynamic configuration)
        {
        }

        public Task<dynamic> Invoke(dynamic input)
        {

            dynamic output = new ExpandoObject();
            output.Result = new HoadingFormB(Mapper.Map<Model>(input)).GeneratePdf();
            return Task.FromResult<dynamic>(output);
        }
    }

    public class HoadingFormB : IDocument
    {
        static HoadingFormB()
        {
            var mangalFont = Assembly.GetExecutingAssembly().GetManifestResourceStream("Factory.HoadingFormBPlugin.Mangal.ttf");
            if (mangalFont != null)
            {
                FontManager.RegisterFont(mangalFont);
            }
            var timesFont = Assembly.GetExecutingAssembly().GetManifestResourceStream("Factory.HoadingFormBPlugin.times.ttf");
            if (timesFont != null)
            {
                FontManager.RegisterFont(timesFont);
            }
        }



        public Model _model { get; }

        public HoadingFormB(Model model)
        {
            _model = model;
        }


        public DocumentMetadata GetMetadata() => DocumentMetadata.Default;

        public void Compose(IDocumentContainer container)
        {
            container
                .Page(page =>
                {
                    page.Margin(30);
                    page.Header().Element(ComposeHeader);
                    page.Content().Element(ComposeContent);
                    // page.Content().Element(ComposeTable);
                    // page.Content().Element(ComposeContent2);


                });
        }
        void ComposeHeader(IContainer container)
        {
            container.Column(column =>
            {
                var stream = Assembly.GetExecutingAssembly().GetManifestResourceStream("Factory.HoadingFormBPlugin.Logo.png");
                if (stream != null)
                {

                    column.Item().Row(row =>
                    {
                        row.RelativeItem(4);
                        row.RelativeItem(1)
                            .AlignCenter()
                            .Height(50)
                            .Image(new BinaryReader(stream).ReadBytes((int)stream.Length), ImageScaling.Resize);
                        row.RelativeItem(4);
                    });
                }
                column.Item()
                    .AlignCenter()
                    .Text(text =>
                    {

                        text.Span("कार्यालयीन उपयोगासाठी").FontSize(9)
                            .FontFamily("Mangal")
                            .FontSize(10).Bold();
                    });
                column.Item()
    .AlignCenter()
    .Text(text =>
    {

        text.Span(" भाग-२").FontSize(9)
            .FontFamily("Mangal")
            .FontSize(10).Bold();
    });

                column.Item()
         .AlignCenter()
         .Text(text =>
         {

             text.Span("तपासणी अधिकारी यांचा अभिप्राय").FontSize(9)
                 .FontFamily("Mangal")
                 .FontSize(10).Bold();
         });
                column.Item()
                      .AlignLeft()
                      .Text(text =>
                      {

                          text.Span(" मा. उप आयुक्त,").FontSize(9)
                              .FontFamily("Mangal")
                              .FontSize(10).Bold();
                      });
                column.Item()
               .AlignLeft()
               .Text(text =>
               {

                   text.Span("पुणे महानगरपालिका यांच्याकडे सादर-").FontSize(9)
                       .FontFamily("Mangal")
                       .FontSize(9);
               });
            });
        }
        void ComposeContent(IContainer container)
        {
            container.Column(column =>
            {
                column.Item()
                  .Text(text =>
                  {
                      //           text.Span($"श्री.")
                      //               .FontFamily("Mangal")
                      //               .FontSize(8).Bold();
                      //           text.Span($"{_model.Name}  ")
                      //      .FontFamily("Times New Roman")
                      //      .FontSize(8).Bold();
                      //           text.Span($"राहणार, पुणे, पेठ ")
                      //              .FontFamily("Mangal")
                      //              .FontSize(8).Bold();
                      //           text.Span($"{_model.Text}")
                      //     .FontFamily("Times New Roman")
                      //     .FontSize(8).Bold();
                      //           text.Span($"घरांक पुणे, यांना दि. ")
                      //                .FontFamily("Mangal")
                      //                .FontSize(8).Bold();
                      //           text.Span($"{_model.FromDate}")
                      //                   .FontFamily("Times New Roman")
                      //                   .FontSize(8).Bold();
                      //           text.Span($"ते  दि. ")
                      // .FontFamily("Mangal")
                      // .FontSize(8).Bold();
                      //   text.Span($"{_model.ToDate} ")
                      //         .FontFamily("Times New Roman")
                      //         .FontSize(8).Bold();
                      text.Span($"आज्ञेप्रमाणे आकाशचिन्ह निरीक्षक यांनी जागेवर तपासणी करून आकाश चिन्हाचे मोजमाप प्रतिज्ञापत्राप्रमाणे बरोबर असल्याची खात्री करून घेतली.")
                             .FontFamily("Mangal")
                             .FontSize(9);

                      text.Span($"आकाश चिन्हाचा प्रकार :")
                   .FontFamily("Mangal")
                   .FontSize(9);
                      text.Span(_model.Type)
                                .FontFamily("Times New Roman")
                                .FontSize(10);
                  });
                // Adding the table with custom styling
                column.Item().PaddingTop(20)
              .MinimalBox()
              .Border(1)
                      .Table(table =>
                      {
                          IContainer DefaultCellStyle(IContainer cellContainer, string backgroundColor)
                          {
                              return cellContainer
                                  .Border(1)
                                  .BorderColor(Colors.Grey.Lighten1)
                                  .Background(backgroundColor)
                                  .PaddingVertical(5)
                                  .PaddingHorizontal(10)
                                  .AlignCenter()
                                  .AlignMiddle();
                          }

                          table.ColumnsDefinition(columns =>
                          {
                              columns.RelativeColumn();
                              columns.ConstantColumn(75);
                              columns.ConstantColumn(75);
                              columns.ConstantColumn(75);
                              columns.ConstantColumn(75);
                              columns.RelativeColumn();
                              columns.RelativeColumn();
                          });

                          table.Header(header =>
                          {
                              header.Cell().RowSpan(2).Element(CellStyle).Text("क्रमांक").FontFamily("Mangal")
                                  .FontSize(8).Bold();
                              header.Cell().RowSpan(2).Element(CellStyle).Text("आकाश चिन्हाचे मोजमाप").FontFamily("Mangal")
                                  .FontSize(8).Bold();
                              header.Cell().RowSpan(2).Element(CellStyle).Text("आकाशचिन्ह लावण्याची मुदत").FontFamily("Mangal")
                                  .FontSize(8).Bold();
                              header.Cell().RowSpan(2).Element(CellStyle).Text("परवाना फीचा दर").FontFamily("Mangal")
                                  .FontSize(8).Bold();
                              header.Cell().RowSpan(2).Element(CellStyle).Text("एक महिन्याची परवाना फी रुपये").FontFamily("Mangal")
                                                               .FontSize(8).Bold();
                              header.Cell().RowSpan(2).Element(CellStyle).Text("३ महिन्याची परवाना फी रुपये").FontFamily("Mangal")
                                                                                          .FontSize(8).Bold();
                              header.Cell().RowSpan(2).Element(CellStyle).Text("शेरा").FontFamily("Mangal")
                                                                                                                     .FontSize(8).Bold();


                              //   header.Cell().Element(CellStyle).Text("पासून").FontFamily("Mangal")
                              //       .FontSize(8).Bold();
                              //   header.Cell().Element(CellStyle).Text("पर्यंत").FontFamily("Mangal")
                              //       .FontSize(8).Bold();

                              // you can extend existing styles by creating additional methods
                              IContainer CellStyle(IContainer cellContainer) => DefaultCellStyle(cellContainer, Colors.Grey.Lighten3);
                          });

                          table.Cell().Element(CellStyle).Text(_model.SNo).FontSize(8);
                          table.Cell().Element(CellStyle).Text(_model.Dimenstion).FontSize(8);
                          table.Cell().Element(CellStyle).Text(_model.Duration).FontSize(8);
                          table.Cell().Element(CellStyle).Text(_model.LicenseFee).FontSize(8);
                          table.Cell().Element(CellStyle).Text(_model.LicenseFeeFormonth).FontSize(8);
                          table.Cell().Element(CellStyle).Text(_model.LicenseFeeFor3month).FontSize(8);
                          table.Cell().Element(CellStyle).Text(_model.Signature).FontSize(8);

                          IContainer CellStyle(IContainer cellContainer) => DefaultCellStyle(cellContainer, Colors.White).ShowOnce();
                      });
                column.Item().PaddingTop(30)
               .Text(text =>
               {

                   text.Span($"सदर जाहिरात फलक/बॅनर/नामफलक मुंबई प्रांतिक महानगरपालिका अधिनियम  १९४९ कलम २४४ प्रमाणे आकाशचिन्ह स्वरूपात येत असल्यामुळे अर्जदारास वरील मुदतीची ‘परवाना फी’ भरण्याची सूचना पाठविण्यास व फी वसूल झाल्यावर परवाना देण्यास आपली मान्यता मिळण्यास नम्र विनंती आहे").FontSize(8)
                       .FontFamily("Mangal")
                       .FontSize(8).Bold();
                   //    text.Span($" {_model.CurrentDate}").FontSize(8)
                   //   .FontFamily("Times New Roman")
                   //   .FontSize(8).Bold();
                   //    text.Span($" पैसे फक्त दि. ").FontSize(8)
                   // .FontFamily("Mangal")
                   // .FontSize(8).Bold();
                   //    text.Span($"{_model.Number1}").FontSize(8)
                   // .FontFamily("Times New Roman")
                   // .FontSize(8).Bold();
                   //    text.Span($" रोजी चलन क्र.").FontSize(8)
                   //  .FontFamily("Mangal")
                   //  .FontSize(8).Bold();
                   //    text.Span($"{_model.Number1}").FontSize(8)
                   //                    .FontFamily("Times New Roman")
                   //                    .FontSize(8).Bold();
                   //    text.Span($" ने महानगरपालिकेच्या पोस्ट कार्यालयात भरणा केली आहे .").FontSize(8)
                   //                                     .FontFamily("Mangal")
                   //                                     .FontSize(8).Bold();
               });

                column.Item().PaddingTop(20).Row(row =>
                {
                    row.RelativeItem(1)
                            .AlignCenter().Text(text =>
                            {
                                text.Span($"माननीयांस विदित व्हावे दिनांक:")
                                .FontFamily("Mangal")
                            .FontSize(8);
                                text.Span($"{_model.CurrentDate} ")
                                  .FontFamily("Times New Roman")
                              .FontSize(8);
                            });
                    row.RelativeItem(1)
                            .AlignCenter().Text(text =>
                            {
                                text.Line("परवाना निरीक्षक")
                                .FontFamily("Mangal")
                            .FontSize(8);
                                text.Line("परवाना व आकाशचिन्ह विभाग")
                                   .FontFamily("Mangal")
                               .FontSize(8);
                                text.Line("क्षेत्रीय कार्यालय, पुणे महानगरपालिका")
                                  .FontFamily("Mangal")
                              .FontSize(8);
                            });
                    row.RelativeItem(1)
                  .AlignRight().Text(text =>
                  {
                      text.Line("महापालिका सहाय्यक, आयुक्त ")
                               .FontFamily("Mangal")
                           .FontSize(8);
                      text.Line("सहाय्यक आयुक्त कार्यालय")
                         .FontFamily("Mangal")
                     .FontSize(8);
                      text.Line("पुणे महानगरपालिका.")
                        .FontFamily("Mangal")
                    .FontSize(8);
                  });
                });
                column.Item()
              .AlignLeft()
                .Text(text =>
                {
                    text.Line("परवाना निरीक्षक,").FontSize(10)
                 .FontFamily("Mangal")
                 .FontSize(8).Bold();
                    text.Line("   यांस परत-").FontSize(10)
                   .FontFamily("Mangal")
                   .FontSize(8).Bold();
                });
                column.Item()
              .AlignLeft()
                .Text(text =>
                {
                    text.Line("   अर्जदारास वरील मुदतीची आकाश चिन्हाची ‘परवाना फी’ भरण्याची सूचना पाठविण्यास व फी वसूल झाल्यावर ‘परवाना’ देण्यात मान्यता देण्यात येत आहे.").FontSize(9)
                 .FontFamily("Mangal")
                 .FontSize(8).Bold();
                });
                column.Item().PaddingTop(20).Row(row =>
                {
                    row.RelativeItem(1)
                                             .AlignCenter().Text(text =>
                                             {
                                                 text.Line("प्रमुख परवाना निरीक्षक")
                                                 .FontFamily("Mangal")
                                             .FontSize(8);
                                                 text.Line("परवाना व आकाशचिन्ह विभाग")
                                                    .FontFamily("Mangal")
                                                .FontSize(8);
                                                 text.Line("पुणे महानगरपालिका ")
                                                   .FontFamily("Mangal")
                                               .FontSize(8);
                                             });
                    row.RelativeItem(1)
                  .AlignCenter().Text(text =>
                  {
                      text.Line("उप आयुक्त ")
                               .FontFamily("Mangal")
                           .FontSize(8);
                      text.Line("परवाना व आकाशचिन्ह विभाग")
                         .FontFamily("Mangal")
                     .FontSize(8);
                      text.Line("पुणे महानगरपालिका")
                        .FontFamily("Mangal")
                    .FontSize(8);
                  });
                });












                //     column.Item().PaddingVertical(5).LineHorizontal(1).LineColor(Colors.Grey.Darken1);
                //     column.Item()
                //    .AlignCenter()
                //      .Text(text =>
                //      {
                //          text.Span("परवान्याबाबत  अटी").FontSize(9)
                //       .FontFamily("Mangal")
                //       .FontSize(9).Bold();
                //      });
                //     column.Item().Text(text =>
                //      {
                //          text.Span(@"
                // (१) परवाना धरणा करणाऱ्याने आकाशचिन्ह जाहिरात फलक महानगरपालिकेने मंजूर केलेल्या ठिकाणीच उभे केले पाहिजेत तसेच ज्या कालावधीपर्यंत आकाश चिन्ह जाहिरात फलक लावण्यास परवानगी दिलेली आहे त्या मुदतीत आकाश चिन्ह तथा जाहिरात फलक सुस्थितीत ठेवण्याची संपूर्ण जबाबदारी अर्जदारावर राहील 
                // (२) परवानाधारकाने जाहिरात फलकावर दिसेल अशा ठिकाणी एका बाजूस परवाना क्रमांक व जाहिरातदार संस्थेचे नाव अथवा संस्थेचे चिन्ह लावणे बंधनकारक आहे
                // (३) आकाशचिन्ह तथा जाहिरात फलक प्रत्यक्ष उभा करण्यापूर्वी अर्जासमवेत देण्यात आलेल्या तांत्रिक अभियंत्याची प्रमाणपत्रानुसार प्रत्यक्ष उभारणी केली असल्याचा दाखला परवानाधारकाने परवाना दिल्यापासून मनपाने दिलेल्या मुदतीत महापालिकेकडे दाखल करावा व त्यानंतरच प्रत्यक्षात जाहिरात फलक तथा आकाशचिन्ह उभे करावे आकाशचिन्ह तथा जाहिरात हलका मुळे कोणत्याही प्रकारचा धोका किंवा नुकसान होणार नाही याची दक्षता संबंधित परवानाधारकाने घ्यावी अशा प्रकारच्या अपघातास अथवा नुकसानिस मनपा जबाबदार राहणार नाही.
                // (४) ज्या आकाशचिन्हास तथा जाहिरात फलकास परवाना दिलेला आहे अशा आकाश चिन्हास
                // (१) मनपा पूर्व मान्यते शिवाय ते बदल केला असेल
                // (२) अपघातामुळे अथवा इतर कोणत्याही नैसर्गिक अथवा अनैसर्गिक कारणांमुळे आकाश चिन्हाचा तथा जाहिरात फलकाचा अथवा त्याचा कोणताही भाग पडेल.
                // (३) ज्या इमारतीवर आकाशचिन्ह तथा जाहिरात फलक उभे करण्यात आले आहेत त्या इमारतीबाबत कोणतीही कायदेशीर कारवाई करण्याचा निर्णय मनपाने घेतला असेल.
                // (४) चार संबंधित इमारतीच्या मालकाने इमारतीच्या बांधकामांमध्ये फेरबदल करण्याचा प्रस्ताव मनपाकडे सादर केला असेल त्यास मनपाने परवानगी दिली असेल.
                //       अशावेळी परवाना धारकास देण्यात आलेला परवाना रद्द समजण्यात येईल.
                // (५) सार्वजनिक उपक्रमासाठी विशेषत रस्ता रुंदीमुळे एखादा जाहिरात फलक बाधित होत असेल तर त्याबाबत मनपाणे संबंधित जाहिरात धारकास तशी लेखी समज दिली असेल तर मनपा ने दिलेल्या मुदतीच्या आत परवानाधारकास स्वखर्चाने जाहिरात फलक(त्यासाठी केलेल्या बांधकामासह) काढून घ्यावा लागेल.अन्यथा मनपा प्रशासनातर्फे फलक व त्यासाठी केलेले बांधकाम काढून टाकण्यात येईल व त्यासाठी येणारा खर्च संबंधितांकडून एकरकमी वसूल केला जाईल.
                // (६) क्रमांक ४ व ५ मुळे जाहिरात फलक बाधित असल्यास त्यासाठी कोणतीही पर्यायी व्यवस्था करण्याची जबाबदारी मनपावर राहणार नाही.
                // (७) हा परवाना, परवाना दिनांक पासून अमलात येईल.परवानाधारकाने परवाना दिल्या तारखेपासून पाणी दिलेल्या मनपा ने दिलेल्या मुदतीच्या आत सहा महिन्यांच्या भाड्या इतकी रक्कमअनामत म्हणून भरावी लागेल तसेच वार्षिक फी ची रक्कम प्रत्येक सहामाहीसाठी आगाऊ भरावी लागेल.फी आकारणीसाठी सहामाही खालील प्रमाणे राहील.
                // (१)  १ एप्रिल ते ३० सप्टेंबर
                // (२)  १ ऑक्टोंबर ते ३१ मार्च
                // (८) परवानाधारकास विहित मुदतीनंतर जाहिरात फलक पुढे चालू ठेवायचा असल्यास विहित मुदत संपण्यापूर्वी किमान 30 दिवस अगोदर मुदत वाढीसाठी रीतसर अर्ज करावा.मात्र मुदतवाढ देण्याचे कोणतेही बंधन मनपा प्रशासनावर राहणार नाही.अशा प्रकारे मुदत वाढ न दिल्यास लावण्यात आलेल्या आकाश चिन्ह वा जाहिरात फलक काढून घेण्याची जबाबदारी जाहिरातदारावर राहील.
                // (९) देण्यात आलेला परवाना फक्त परवानाधारकालाच उपयोगात आणता येईल इतर कोणतीही व्यक्ती अगर संस्था यांना मनपाच्या पूर्व मान्यते शिवाय त्याचा वापर करता येणार नाही
                // (१०) हा परवाना दिनांक …………..पर्यंत मात्र पात्र राहील.")
                //               .FontFamily("Mangal")
                //            .FontSize(7);
                //      });
                //             column.Item().Row(row =>
                //   {
                //       row.RelativeItem(1)
                //             .AlignRight().Text(text =>
                //             {
                //                 text.Span("माननीय उप-आयुक्त ( विशेष )")
                //                 .FontFamily("Mangal")
                //             .FontSize(9);
                //             });
                //   });

            });
        }
    }

    public static class Mapper
    {
        public static T Map<T>(ExpandoObject source) where T : class
        {
            T destination = (T)Activator.CreateInstance(typeof(T));
            IDictionary<string, object> dict = source;
            var type = destination.GetType();

            foreach (var prop in type.GetProperties())
            {
                var lower = prop.Name.ToLower();
                Console.WriteLine($"Mapping {lower}");
                var key = dict.Keys.SingleOrDefault(k => k.ToLower() == lower);

                if (key != null)
                {
                    prop.SetValue(destination, CastProperty(dict[key], prop.PropertyType.ToString()), null);
                }
            }
            return destination;
        }

        static dynamic CastProperty(dynamic property, string type)
        {
            switch (type)
            {
                case "System.String":
                    return (String)property;
                case "System.Decimal":
                    return (Decimal)property;
                default:
                    return property;

            }
        }
        public class Model
        {
            public string LicenseNumber { get; set; }
            public string Type { get; set; }
            public string CurrentDate { get; set; }
            public string Duration { get; set; }
            public string LicenseFee { get; set; }
            public string LicenseFeeFormonth { get; set; }
            public string LicenseFeeFor3month { get; set; }
            public string Name { get; set; }
            public string FromDate { get; set; }
            public string ToDate { get; set; }
            public string Number1 { get; set; }
            public string Number2 { get; set; }
            public string Text { get; set; }
            public string SNo { get; set; }
            public string Place { get; set; }
            public string Dimenstion { get; set; }
            public string From { get; set; }
            public string To { get; set; }
            public string Signature { get; set; }
        }


    }
}
