<Project Sdk="Microsoft.NET.Sdk">


  <ItemGroup>
  <ProjectReference Include="..\Factory.CcAvenuePlugin\Factory.CcAvenuePlugin.csproj" />
    <ProjectReference Include="..\Factory.LicenseCertificatePlugin\Factory.LicenseCertificatePlugin.csproj" />
   <ProjectReference Include="..\Factory.CredaiBankReceiptPlugin\Factory.CredaiBankReceiptPlugin.csproj" />
   <ProjectReference Include="..\Factory.CredaiInvoicePlugin\Factory.CredaiInvoicePlugin.csproj" />
    <ProjectReference Include="..\Factory.CredaiCertificatePlugin\Factory.CredaiCertificatePlugin.csproj" />
    <ProjectReference Include="..\Factory.CcaAvenueInvoicePlugin\Factory.CcaAvenueInvoicePlugin.csproj" />
   <ProjectReference Include="..\Factory.MembershipCertificatePlugin\Factory.MembershipCertificatePlugin.csproj" />
    <ProjectReference Include="..\Factory.DCNCertificatePlugin\Factory.DCNCertificatePlugin.csproj" />
    <ProjectReference Include="..\Factory.DCNToBlockPlugin\Factory.DCNToBlockPlugin.csproj" />
    <ProjectReference Include="..\Factory.GlobalSMSPlugin\Factory.GlobalSMSPlugin.csproj" />
    <ProjectReference Include="..\Factory.HttpPlugin\Factory.HttpPlugin.csproj" />
    <ProjectReference Include="..\Factory.QRCodePlugin\Factory.QRCodePlugin.csproj" />
    <ProjectReference Include="..\Factory.SECertificatePlugin\Factory.SECertificatePlugin.csproj" />
    <ProjectReference Include="..\Factory.SMTPPlugin\Factory.SMTPPlugin.csproj" />
    <ProjectReference Include="..\Factory.RMSHSMPlugin\Factory.RMSHSMPlugin.csproj" />
    <ProjectReference Include="..\Factory.RecommendedAssociateCertificatePlugin\Factory.RecommendedAssociateCertificatePlugin.csproj" />
<ProjectReference Include="..\Factory.RecommendedOrdinaryCertificatePlugin\Factory.RecommendedOrdinaryCertificatePlugin.csproj" />
<ProjectReference Include="..\Factory.LicenseCertificateCredAiPlugin\Factory.LicenseCertificateCredAiPlugin.csproj" />
<ProjectReference Include="..\Factory.BankReceiptCredAiPlugin\Factory.BankReceiptCredAiPlugin.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="7.0.0" />
  </ItemGroup>

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

</Project>
