using QuestPDF.Fluent;
using QuestPDF.Infrastructure;
using QuestPDF.Drawing;
using Svg.Skia;
using Factory.Plugins;
using Factory.Models;
using System.Reflection;
using System.Dynamic;
using QuestPDF.Helpers;
using SkiaSharp;
using static Factory.CredaiInvoicePlugin.Mapper;

namespace Factory.CredaiInvoicePlugin
{
    public class CredaiInvoicePlugin : IRemoteCallPlugin
    {
        public string GetName()
        {
            return "CredaiInvoice";
        }

        public List<string> GetMethods()
        {
            return new List<string>() {
                "Generate"
            };
        }

        public string GetHelp()
        {
            return "";
        }

        public List<FieldMeta> GetConfigurationMeta()
        {
            return new List<FieldMeta>() {
            new FieldMeta()
            {
                Name = "Logo",
                Category = FieldCategory.Primitive,
                IsCollection = false,
                IsRequired = true,
                IsUnique = false,
                FileExtension = ".",
                StorageType = StorageType.Plain,
                Type = PrimitiveType.File.ToString()
            }
             };
        }


        // private byte[] _logo { get; set; }
        public void Configure(dynamic configuration)
        {
            // var logoBase64String = ((IDictionary<string, object>)configuration)["Logo"].ToString();
            // _logo = Convert.FromBase64String(logoBase64String);
        }

        public Task<dynamic> Invoke(dynamic input)
        {
            dynamic output = new ExpandoObject();
            output.Result = new CredaiInvoice(Mapper.Map<Model>(input)).GeneratePdf();
            return Task.FromResult<dynamic>(output);
        }
    }

    public class CredaiInvoice : IDocument
    {

        // private byte[] _logo;
        static CredaiInvoice()
        {

            var calibri = Assembly.GetExecutingAssembly().GetManifestResourceStream("Factory.CredaiInvoicePlugin.times.ttf");
            if (calibri != null)
            {
                FontManager.RegisterFont(calibri);
            }
        }


        public Model _model { get; }

        public CredaiInvoice(Model model)
        {
            _model = model;
            // _logo = logo;
        }

        public DocumentMetadata GetMetadata() => DocumentMetadata.Default;
        public void Compose(IDocumentContainer container)
        {

            container
                .Page(page =>
                {
                    page.Margin(25);
                    page.Header().Element(ComposeHeader); // Place the header on the first page only
                    page.Content().Element(ComposeContent);
                    // page.Footer().Element(ComposeFooter);
                });
        }

        static IContainer CellStyle(IContainer container) => container.BorderBottom(1).BorderColor(Colors.Grey.Lighten2).PaddingVertical(10);
        void ComposeHeader(IContainer container)
        {

            container.Row(row =>
                                  {
                                      row.RelativeItem().Column(column =>
                                      {

                                          //   column.Item().Row(row =>
                                          //   {
                                          //       row.RelativeItem(1)
                                          //               .Height(50)
                                          //         .Width(101)
                                          //           .Image(_logo);
                                          //       row.RelativeItem(4);
                                          //   });
                                          //   }
                                          column.Item().Row(row =>
                    {
                        column
            .Item()
            .AlignRight()
            .Padding(1)
            .Width(2 * 40) // sizes from 80x40 to 240x120
            .Height(4 * 20)
            .Scale(2)
            .Canvas((canvas, space) =>
               {
                   Console.WriteLine("=========================>STARTED2");
                   using var svg = new SKSvg();

                   try
                   {
                       svg.FromSvg(_model.QrCodeUrl);
                       Console.WriteLine("=========================>STARTED3");
                       canvas.DrawPicture(svg.Picture);
                   }
                   catch (Exception ex)
                   {
                       Console.WriteLine($"EXCEPTION=========================>{ex.Message}");
                   }
                   Console.WriteLine("=========================>STARTED4");
               });
                        Console.WriteLine("=========================>STARTED5");
                        column.Item().Row(row =>
         {


             //   row.RelativeItem(4).AlignTop();
             //   row.RelativeItem(1)
             //                 .AlignCenter()
             //                 .Height(50)
             //                 .Image(_logo, ImageScaling.Resize);
             //   row.RelativeItem(4).AlignTop();
         });
                    }
                    );
                                          column.Item()
.AlignCenter()
.Text(text =>
{
    text.Span("Tax Invoice").FontSize(10)
    .FontSize(10).Bold();
});

                                          column.Item().PaddingTop(35).Text(text =>
                                                                {
                                                                    text.Span($"IRN  :").FontSize(8).Bold();
                                                                    text.Line($" b23e5316044e5ca6deb83fb1d40d41268bfdd9e13f1ba32-e32295949cbc6a86").FontSize(8).Bold();
                                                                    text.Span($"Ack No  :").FontSize(8).Bold();
                                                                    text.Line($" 122318192293315").FontSize(8).Bold();
                                                                    text.Span($"Ack Date  :").FontSize(8).Bold();
                                                                    text.Line($" 14-Sep-23").FontSize(8).Bold();
                                                                });




                                      });
                                  });







            //             container
            //  //  .Padding(10)
            //  .MinimalBox()
            //  .Border(1)
            //  .Table(table =>
            //  {
            //      IContainer DefaultCellStyle(IContainer container, string backgroundColor)
            //      {
            //          return container
            //              .Border(1)
            //              .BorderColor(Colors.Grey.Lighten1)
            //              //.Background(backgroundColor)
            //              .PaddingVertical(5)
            //              .PaddingHorizontal(10)
            //              .AlignCenter()
            //              .AlignMiddle();
            //      }

            //      table.ColumnsDefinition(columns =>
            //      {
            //          columns.RelativeColumn();

            //          columns.ConstantColumn(75);
            //          columns.ConstantColumn(75);

            //          columns.ConstantColumn(75);
            //          columns.ConstantColumn(75);
            //      });

            //      table.Header(header =>
            //      {


            //          header.Cell().RowSpan(4).Element(CellStyle).ExtendHorizontal().AlignLeft().Text(text =>
            //          {
            //              text.Line("CREDAI - Pune Metro").FontSize(8).Bold();
            //              text.Line("OFFICE NO. T-1, 3RD FLOOR").FontSize(8);
            //              text.Line("NUCLEUS JEEJEEBHOY TOWERS").FontSize(8);
            //              text.Line("CHURCH ROAD, PUNE 411001").FontSize(8);
            //              text.Line("GSTIN/UIN: 27**********1Z6").FontSize(8);
            //              text.Line("State Name : Maharashtra, Code : 27").FontSize(8);
            //              text.Line("E-Mail : <EMAIL>").FontSize(8);

            //          });

            //          header.Cell().ColumnSpan(2).Element(CellStyle).Text("Invoice No.").FontSize(8);
            //          header.Cell().ColumnSpan(2).Element(CellStyle).Text("Dated").FontSize(8);
            //          header.Cell().ColumnSpan(2).Element(CellStyle).Text("Delivery Note").FontSize(8); ;
            //          header.Cell().ColumnSpan(2).Element(CellStyle).Text("Mode/Terms of Payment").FontSize(8); ;
            //          header.Cell().ColumnSpan(2).Element(CellStyle).Text("Reference No. & Date.").FontSize(8); ;
            //          header.Cell().ColumnSpan(2).Element(CellStyle).Text("Other References").FontSize(8); ;
            //          header.Cell().ColumnSpan(2).Element(CellStyle).Text("Buyer’s Order No.").FontSize(8); ;
            //          header.Cell().ColumnSpan(2).Element(CellStyle).Text("Dated").FontSize(8); ;

            //          header.Cell().RowSpan(3).Element(CellStyle).ExtendHorizontal().AlignLeft().Text(text =>
            //          {
            //              text.Line("Buyer (Bill to)").FontSize(8);
            //              text.Line("SMP Namrata Associates").FontSize(8);
            //              text.Line("448, Mangalwar Peth, Pune 411011").FontSize(8);
            //              text.Line("GSTIN/UIN : 27**********1ZP").FontSize(8);
            //              text.Line("PAN/IT No : **********").FontSize(8);
            //              text.Line("State Name : Maharashtra, Code : 27").FontSize(8);

            //          });

            //          header.Cell().ColumnSpan(2).Element(CellStyle).Text("Dispatch Doc No.").FontSize(8); ;
            //          header.Cell().ColumnSpan(2).Element(CellStyle).Text("Delivery Note Date").FontSize(8); ;
            //          header.Cell().ColumnSpan(2).Element(CellStyle).Text("Dispatched through").FontSize(8); ;
            //          header.Cell().ColumnSpan(2).Element(CellStyle).Text("Destination").FontSize(8); ;
            //          header.Cell().ColumnSpan(4).Element(CellStyle).Text("Terms of Delivery").FontSize(8); ;
            //          IContainer CellStyle(IContainer container) => DefaultCellStyle(container, Colors.Grey.Lighten3);
            //      });
            //  });
        }





        void ComposeContent(IContainer container)


        {
            container.Column(column =>
  {
      column.Item()
                                .MinimalBox()
                                 .Border(1)
                                 .Table(table =>
                                 {
                                     IContainer DefaultCellStyle(IContainer container, string backgroundColor)
                                     {
                                         return container
                                                 .Border(1)
                                                 .BorderColor(Colors.Grey.Lighten1)
                                                 //.Background(backgroundColor)
                                                 .PaddingVertical(5)
                                                 .PaddingHorizontal(10)
                                                 .AlignCenter()
                                                 .AlignMiddle();
                                     }

                                     table.ColumnsDefinition(columns =>
                                         {
                                             columns.RelativeColumn();

                                             columns.ConstantColumn(75);
                                             columns.ConstantColumn(75);

                                             columns.ConstantColumn(75);
                                             columns.ConstantColumn(75);
                                         });

                                     table.Header(header =>
                                         {


                                             header.Cell().RowSpan(4).Element(CellStyle).ExtendHorizontal().AlignLeft().Text(text =>
                                             {
                                                 text.Line("CREDAI - Pune Metro").FontSize(8).Bold();
                                                 text.Line("OFFICE NO. T-1, 3RD FLOOR").FontSize(8);
                                                 text.Line("NUCLEUS JEEJEEBHOY TOWERS").FontSize(8);
                                                 text.Line("CHURCH ROAD, PUNE 411001").FontSize(8);
                                                 text.Line("GSTIN/UIN: 27**********1Z6").FontSize(8);
                                                 text.Line("State Name : Maharashtra, Code : 27").FontSize(8);
                                                 text.Line("E-Mail : <EMAIL>").FontSize(8);

                                             });

                                             header.Cell().ColumnSpan(2).Element(CellStyle).Text("Invoice No.").FontSize(8);
                                             header.Cell().ColumnSpan(2).Element(CellStyle).Text("Dated").FontSize(8);
                                             header.Cell().ColumnSpan(2).Element(CellStyle).Text("Delivery Note").FontSize(8); ;
                                             header.Cell().ColumnSpan(2).Element(CellStyle).Text("Mode/Terms of Payment").FontSize(8); ;
                                             header.Cell().ColumnSpan(2).Element(CellStyle).Text("Reference No. & Date.").FontSize(8); ;
                                             header.Cell().ColumnSpan(2).Element(CellStyle).Text("Other References").FontSize(8); ;
                                             header.Cell().ColumnSpan(2).Element(CellStyle).Text("Buyer’s Order No.").FontSize(8); ;
                                             header.Cell().ColumnSpan(2).Element(CellStyle).Text("Dated").FontSize(8); ;

                                             header.Cell().RowSpan(3).Element(CellStyle).ExtendHorizontal().AlignLeft().Text(text =>
                                             {
                                                 text.Line("Buyer (Bill to)").FontSize(8);
                                                 text.Line(_model.BuyerName).FontSize(8);
                                                 text.Line(_model.Address).FontSize(8);
                                                 text.Line($"GSTIN/UIN :{_model.Gst} ").FontSize(8);
                                                 text.Line($"PAN/IT No : {_model.Pan}").FontSize(8);
                                                 text.Line($"State Name : {_model.State}").FontSize(8);

                                             });

                                             header.Cell().ColumnSpan(2).Element(CellStyle).Text("Dispatch Doc No.").FontSize(8); ;
                                             header.Cell().ColumnSpan(2).Element(CellStyle).Text("Delivery Note Date").FontSize(8); ;
                                             header.Cell().ColumnSpan(2).Element(CellStyle).Text("Dispatched through").FontSize(8); ;
                                             header.Cell().ColumnSpan(2).Element(CellStyle).Text("Destination").FontSize(8); ;
                                             header.Cell().ColumnSpan(4).Element(CellStyle).Text("Terms of Delivery").FontSize(8); ;
                                             IContainer CellStyle(IContainer container) => DefaultCellStyle(container, Colors.Grey.Lighten3);
                                         });
                                 });











      column.Item()
                // .Padding(10)
                .MinimalBox()
                .Border(1)
                .Table(table =>
                {
                    IContainer DefaultCellStyle(IContainer container, string backgroundColor)
                    {
                        return container
                            .Border(1)
                            .BorderColor(Colors.Grey.Lighten1)
                            //.Background(backgroundColor)
                            .PaddingVertical(10)
                            .PaddingHorizontal(10)
                            .AlignCenter()
                            .AlignMiddle();
                    }

                    table.ColumnsDefinition(columns =>
                        {
                            columns.RelativeColumn();

                            columns.ConstantColumn(75);
                            columns.ConstantColumn(75);
                        });

                    table.Header(header =>
                        {


                            header.Cell().Element(CellStyle).Text("Particulars").FontSize(8);

                            header.Cell().Element(CellStyle).Text("HSN/SAC").FontSize(8);
                            header.Cell().Element(CellStyle).Text("Amount").FontSize(8);
                            table.Cell().RowSpan(1).Element(CellStyle).AlignLeft().PaddingRight(150).PaddingTop(5).Text(text =>
                                {
                                    text.Line($"Entrance Fee Received").FontSize(8).Bold();
                                    text.Line($"Annual Membership-{_model.Type} Member").FontSize(8).Bold();
                                    text.Line($"For F Y 23-24").FontSize(7);
                                    text.Line($"CGST").FontSize(8).Bold();
                                    text.Line($"SGST").FontSize(8).Bold();
                                });

                            table.Cell().RowSpan(1).Element(CellStyle).AlignLeft().PaddingBottom(35).Text(text =>
                                {
                                    text.Line($"999599").FontSize(8).Bold();
                                    text.Line($"999599").FontSize(8).Bold();
                                });
                            table.Cell().RowSpan(1).Element(CellStyle).AlignLeft().PaddingBottom(10).Text(text =>
                                {
                                    text.Line($"{_model.EntranceFee}").FontSize(8).Bold();
                                    text.Line($"{_model.AnnualGst}").FontSize(8).Bold();
                                    text.Line($"{_model.Cgst}").FontSize(8).Bold();
                                    text.Line($"{_model.Sgst}").FontSize(8).Bold();
                                });

                            table.Cell().RowSpan(1).Element(CellStyle).AlignRight().PaddingLeft(330).Text(text =>
                                {
                                    text.Line($"Total").FontSize(8);
                                });
                            table.Cell().RowSpan(1).Element(CellStyle).Text(text =>
                                {
                                    text.Line($"").FontSize(8);
                                });
                            table.Cell().RowSpan(1).Element(CellStyle).Text(text =>
                                {
                                    text.Line($"{_model.Total}").FontSize(8).Bold();
                                });

                            table.Cell().ColumnSpan(3).Element(CellStyle).AlignLeft().PaddingRight(300).Text(text =>
                                {
                                    text.Line($"Amount Chargeable (in words)").FontSize(6);
                                    text.Line($"Indian Rupees {ConvertNumberToWords(_model.Total)} Only").FontSize(8).Bold();
                                });

                            IContainer CellStyle(IContainer container) => DefaultCellStyle(container, Colors.Grey.Lighten3);
                            // ComposeFooter1(container);
                        });
                });
      column.Item()
                                .MinimalBox()
                               .Border(1)
                               .Table(table =>
                               {
                                   IContainer DefaultCellStyle(IContainer container, string backgroundColor)
                                   {
                                       return container
                                               .Border(1)
                                               .BorderColor(Colors.Grey.Lighten1)
                                               // //.Background(backgroundColor)
                                               .PaddingVertical(5)
                                               .PaddingHorizontal(10)
                                               .AlignCenter()
                                               .AlignMiddle();
                                   }

                                   table.ColumnsDefinition(columns =>
                                       {
                                           columns.RelativeColumn();
                                           columns.ConstantColumn(75);
                                           columns.ConstantColumn(75);
                                           columns.ConstantColumn(75);
                                           columns.ConstantColumn(75);
                                           // columns.RelativeColumn();
                                           columns.ConstantColumn(75);

                                           columns.ConstantColumn(75);

                                       });

                                   table.Header(header =>
                                       {

                                           header.Cell().RowSpan(2).Element(CellStyle).Text("HSN/SAC").FontSize(8);
                                           header.Cell().RowSpan(2).Element(CellStyle).Text("Taxable Value").FontSize(8);
                                           header.Cell().ColumnSpan(2).Element(CellStyle).Text("CGST").FontSize(8);
                                           header.Cell().ColumnSpan(2).Element(CellStyle).Text("SGST/UTGST").FontSize(8);
                                           header.Cell().RowSpan(2).Element(CellStyle).Text("Total Tax Amount").FontSize(8);
                                           header.Cell().Element(CellStyle).Text("Rate").FontSize(8);
                                           header.Cell().Element(CellStyle).Text("Amount").FontSize(8);
                                           header.Cell().Element(CellStyle).Text("Rate").FontSize(8);
                                           header.Cell().Element(CellStyle).Text("Amount").FontSize(8);



                                           header.Cell().Element(CellStyle).Text("999599").FontSize(8);
                                           header.Cell().Element(CellStyle).Text("7,000.00").FontSize(8);
                                           header.Cell().Element(CellStyle).Text("9%").FontSize(8);
                                           header.Cell().Element(CellStyle).Text("630.00").FontSize(8);
                                           header.Cell().Element(CellStyle).Text("9%").FontSize(8);
                                           header.Cell().Element(CellStyle).Text("630.00").FontSize(8);
                                           header.Cell().Element(CellStyle).Text("1,260.00").FontSize(8);


                                           header.Cell().Element(CellStyle).Text("Total").FontSize(8);
                                           header.Cell().Element(CellStyle).Text("7,000.00").FontSize(8);
                                           header.Cell().Element(CellStyle).Text("").FontSize(8);
                                           header.Cell().Element(CellStyle).Text("630.00").FontSize(8);
                                           header.Cell().Element(CellStyle).Text("").FontSize(8);
                                           header.Cell().Element(CellStyle).Text("630.00").FontSize(8);
                                           header.Cell().Element(CellStyle).Text("1,260.00").FontSize(8);





                                           header.Cell().ColumnSpan(7).Element(CellStyle).ExtendHorizontal().AlignLeft().Text(text =>
                                           {
                                               text.Span("Tax Amount (in words)").FontSize(8); ;
                                               text.Line("Indian Rupees One Thousand Two Hundred Sixty Only").Bold().FontSize(8); ;
                                               text.Line("Remarks :").FontSize(8); ;
                                               text.Line("Receipt No.18427").FontSize(8); ;
                                               text.Span("Company’s PAN :").FontSize(8); ;
                                               text.Line("**********").Bold().FontSize(8); ;

                                           });
                                           // header.Cell().RowSpan(1).Element(CellStyle).Text("FEFEFE").FontSize(8);
                                           // header.Cell().RowSpan(1).Element(CellStyle).Text("FEFEFE").FontSize(8);
                                           //     header.Cell().ColumnSpan(7).Element(CellStyle).ExtendHorizontal().AlignRight().Text(text =>
                                           //    {
                                           //        //    text.Line("wswswswswswswsws").FontSize(8);
                                           //        //    text.Line("wswswswswswswsws").FontSize(8);
                                           //        //    text.Line("").FontSize(8);
                                           //        //    text.Line("").FontSize(8);
                                           //        //    text.Line("").FontSize(8);
                                           //        //    text.Line("").FontSize(8);



                                           //    });
                                           // header.Cell().Element(CellStyle).Text("EDEDEDEDEDE").FontSize(8);
                                           IContainer CellStyle(IContainer container) => DefaultCellStyle(container, Colors.Grey.Lighten3);
                                       });
                               });
  });
        }

        void ComposeFooter(IContainer container)
        {
            container.PaddingBottom(250)
    // .Padding(10)
    .MinimalBox()
    .Border(1)
    .Table(table =>
    {
        IContainer DefaultCellStyle(IContainer container, string backgroundColor)
        {
            return container
                                          .Border(1)
                                          .BorderColor(Colors.Grey.Lighten1)
                                          // //.Background(backgroundColor)
                                          .PaddingVertical(5)
                                          .PaddingHorizontal(10)
                                          .AlignCenter()
                                          .AlignMiddle();
        }

        table.ColumnsDefinition(columns =>
                               {
                                   columns.RelativeColumn();
                                   columns.ConstantColumn(75);
                                   columns.ConstantColumn(75);
                                   columns.ConstantColumn(75);
                                   columns.ConstantColumn(75);
                                   // columns.RelativeColumn();
                                   columns.ConstantColumn(75);

                                   columns.ConstantColumn(75);

                               });

        table.Header(header =>
                               {

                                   header.Cell().RowSpan(2).Element(CellStyle).Text("HSN/SAC").FontSize(8);
                                   header.Cell().RowSpan(2).Element(CellStyle).Text("Taxable Value").FontSize(8);
                                   header.Cell().ColumnSpan(2).Element(CellStyle).Text("CGST").FontSize(8);
                                   header.Cell().ColumnSpan(2).Element(CellStyle).Text("SGST/UTGST").FontSize(8);
                                   header.Cell().RowSpan(2).Element(CellStyle).Text("Total Tax Amount").FontSize(8);
                                   header.Cell().Element(CellStyle).Text("Rate").FontSize(8);
                                   header.Cell().Element(CellStyle).Text("Amount").FontSize(8);
                                   header.Cell().Element(CellStyle).Text("Rate").FontSize(8);
                                   header.Cell().Element(CellStyle).Text("Amount").FontSize(8);



                                   header.Cell().Element(CellStyle).Text("999599").FontSize(8);
                                   header.Cell().Element(CellStyle).Text("7,000.00").FontSize(8);
                                   header.Cell().Element(CellStyle).Text("9%").FontSize(8);
                                   header.Cell().Element(CellStyle).Text("630.00").FontSize(8);
                                   header.Cell().Element(CellStyle).Text("9%").FontSize(8);
                                   header.Cell().Element(CellStyle).Text("630.00").FontSize(8);
                                   header.Cell().Element(CellStyle).Text("1,260.00").FontSize(8);


                                   header.Cell().Element(CellStyle).Text("Total").FontSize(8);
                                   header.Cell().Element(CellStyle).Text("7,000.00").FontSize(8);
                                   header.Cell().Element(CellStyle).Text("").FontSize(8);
                                   header.Cell().Element(CellStyle).Text("630.00").FontSize(8);
                                   header.Cell().Element(CellStyle).Text("").FontSize(8);
                                   header.Cell().Element(CellStyle).Text("630.00").FontSize(8);
                                   header.Cell().Element(CellStyle).Text("1,260.00").FontSize(8);





                                   header.Cell().ColumnSpan(7).Element(CellStyle).ExtendHorizontal().AlignLeft().Text(text =>
                                   {
                                       text.Span("Tax Amount (in words)").FontSize(8); ;
                                       text.Line("Indian Rupees One Thousand Two Hundred Sixty Only").Bold().FontSize(8); ;
                                       text.Line("Remarks :").FontSize(8); ;
                                       text.Line("Receipt No.18427").FontSize(8); ;
                                       text.Span("Company’s PAN :").FontSize(8); ;
                                       text.Line("**********").Bold().FontSize(8); ;

                                   });
                                   // header.Cell().RowSpan(1).Element(CellStyle).Text("FEFEFE").FontSize(8);
                                   // header.Cell().RowSpan(1).Element(CellStyle).Text("FEFEFE").FontSize(8);
                                   //     header.Cell().ColumnSpan(7).Element(CellStyle).ExtendHorizontal().AlignRight().Text(text =>
                                   //    {
                                   //        //    text.Line("wswswswswswswsws").FontSize(8);
                                   //        //    text.Line("wswswswswswswsws").FontSize(8);
                                   //        //    text.Line("").FontSize(8);
                                   //        //    text.Line("").FontSize(8);
                                   //        //    text.Line("").FontSize(8);
                                   //        //    text.Line("").FontSize(8);



                                   //    });
                                   // header.Cell().Element(CellStyle).Text("EDEDEDEDEDE").FontSize(8);
                                   IContainer CellStyle(IContainer container) => DefaultCellStyle(container, Colors.Grey.Lighten3);
                               });
    });

        }
        static string ConvertNumberToWords(string numericString)
        {
            string[] digitWords = { "Zero", "One", "Two", "Three", "Four", "Five", "Six", "Seven", "Eight", "Nine" };
            string[] tensWords = { "Ten", "Twenty", "Thirty", "Forty", "Fifty", "Sixty", "Seventy", "Eighty", "Ninety" };
            string[] teensWords = { "Eleven", "Twelve", "Thirteen", "Fourteen", "Fifteen", "Sixteen", "Seventeen", "Eighteen", "Nineteen" };

            if (!string.IsNullOrWhiteSpace(numericString))
            {
                if (numericString == "0")
                {
                    return "zero";
                }

                int number;
                if (int.TryParse(numericString, out number))
                {
                    if (number < 0 || number > 999999)
                    {
                        return "Invalid input";
                    }
                    if (number == 1000)
                    {
                        return "one thousand";
                    }

                    string result = "";

                    int lakhs = number / 100000;
                    int thousands = (number % 100000) / 1000;
                    int hundreds = (number % 1000) / 100;
                    int tens = (number % 100) / 10;
                    int ones = number % 10;

                    if (lakhs > 0)
                    {
                        result += ConvertNumberToWords(lakhs.ToString()) + " lakh";
                    }

                    if (thousands > 0)
                    {
                        if (result != "")
                        {
                            result += " ";
                        }
                        result += ConvertNumberToWords(thousands.ToString()) + " thousand";
                    }

                    if (hundreds > 0)
                    {
                        if (result != "")
                        {
                            result += " ";
                        }
                        result += digitWords[hundreds] + " hundred";
                    }

                    if (tens > 1)
                    {
                        if (result != "")
                        {
                            result += " ";
                        }
                        result += tensWords[tens - 1];
                        if (ones > 0)
                        {
                            result += "-" + digitWords[ones];
                        }
                    }
                    else if (tens == 1)
                    {
                        if (result != "")
                        {
                            result += " ";
                        }
                        result += teensWords[ones - 1];
                    }
                    else if (ones > 0)
                    {
                        if (result != "")
                        {
                            result += " ";
                        }
                        result += digitWords[ones];
                    }

                    return result;
                }
            }

            return "Invalid input";
        }


    }
    public static class Mapper
    {
        public static T Map<T>(ExpandoObject source) where T : class
        {
            T destination = (T)Activator.CreateInstance(typeof(T));
            IDictionary<string, object> dict = source;
            var type = destination.GetType();

            foreach (var prop in type.GetProperties())
            {
                var lower = prop.Name.ToLower();
                Console.WriteLine($"Mapping {lower}");
                var key = dict.Keys.SingleOrDefault(k => k.ToLower() == lower);

                if (key != null)
                {
                    prop.SetValue(destination, CastProperty(dict[key], prop.PropertyType.ToString()), null);
                }
            }
            return destination;
        }

        static dynamic CastProperty(dynamic property, string type)
        {
            switch (type)
            {
                case "System.String":
                    return (String)property;
                case "System.Decimal":
                    return (Decimal)property;
                default:
                    return property;

            }
        }


        public class Model
        {
            public string Type { get; set; }
            public string Total { get; set; }
            public string EntranceFee { get; set; }
            public string AnnualGst { get; set; }
            public string Cgst { get; set; }
            public string Sgst { get; set; }
            public string BuyerName { get; set; }
            public string Address { get; set; }
            public string Gst { get; set; }
            public string Pan { get; set; }
            public string State { get; set; }
            public string InvoiceNumber { get; set; }
            public DateTime IssueDate { get; set; } = DateTime.Now;
            public string QrCodeUrl { get; set; }
            public string BillingName { get; set; }
            // public string Comments { get; set; }
        }

    }

}

