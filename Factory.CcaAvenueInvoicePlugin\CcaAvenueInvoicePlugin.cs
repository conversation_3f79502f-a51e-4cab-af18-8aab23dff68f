using QuestPDF.Fluent;
using QuestPDF.Infrastructure;
using QuestPDF.Drawing;
using Factory.Plugins;
using Factory.Models;
using System.Reflection;
using System.Dynamic;
using QuestPDF.Helpers;
using static Factory.CcaAvenueInvoicePlugin.Mapper;

namespace Factory.CcaAvenueInvoicePlugin
{
    public class CcaAvenueInvoicePlugin : IRemoteCallPlugin
    {
        public string GetName()
        {
            return "CcaAvenueInvoice";
        }

        public List<string> GetMethods()
        {
            return new List<string>() {
                "Generate"
            };
        }

        public string GetHelp()
        {
            return "";
        }

        public List<FieldMeta> GetConfigurationMeta()
        {
            return new List<FieldMeta>() {
            new FieldMeta()
            {
                Name = "Logo",
                Category = FieldCategory.Primitive,
                IsCollection = false,
                IsRequired = true,
                IsUnique = false,
                FileExtension = ".",
                StorageType = StorageType.Plain,
                Type = PrimitiveType.File.ToString()
            }
             };
        }


        private byte[] _logo { get; set; }
        public void Configure(dynamic configuration)
        {
            var logoBase64String = ((IDictionary<string, object>)configuration)["Logo"].ToString();
            _logo = Convert.FromBase64String(logoBase64String);
        }

        public Task<dynamic> Invoke(dynamic input)
        {
            dynamic output = new ExpandoObject();
            output.Result = new CcaAvenueInvoice(Mapper.Map<Model>(input), _logo).GeneratePdf();
            return Task.FromResult<dynamic>(output);
        }
    }

    public class CcaAvenueInvoice : IDocument
    {

        private byte[] _logo;
        static CcaAvenueInvoice()
        {
            // var mangalFont = Assembly.GetExecutingAssembly().GetManifestResourceStream("Factory.CcaAvenueInvoicePlugin.Mangal.ttf");
            // if (mangalFont != null)
            // {
            //     FontManager.RegisterFont(mangalFont);
            // }
            // var timesFont = Assembly.GetExecutingAssembly().GetManifestResourceStream("Factory.CcaAvenueInvoicePlugin.times.ttf");
            // if (timesFont != null)
            // {
            //     FontManager.RegisterFont(timesFont);
            // }
            var calibri = Assembly.GetExecutingAssembly().GetManifestResourceStream("Factory.CcaAvenueInvoicePlugin.LiberationSans-Bold.ttf");
            if (calibri != null)
            {
                FontManager.RegisterFont(calibri);
            }
        }


        public Model _model { get; }

        public CcaAvenueInvoice(Model model, byte[] logo)
        {
            _model = model;
            _logo = logo;
        }

        public DocumentMetadata GetMetadata() => DocumentMetadata.Default;
        public void Compose(IDocumentContainer container)
        {

            container
                .Page(page =>
                {

                    // const float horizontalMargin = 1.0f;
                    const float verticalMargin = 1f;

                    page.Size(PageSizes.A4);
                    // page.MarginVertical(verticalMargin, Unit.Inch);
                    // page.MarginHorizontal(horizontalMargin, Unit.Inch);
                    page.Margin(35);
                    // page.Size(PageSizes.A4);
                    page.Header().Element(ComposeHeader);
                    page.Content().Element(ComposeContent);
                    page.Margin(35);
                    page.Footer().Element(ComposeFooter);
                    // page.Footer().AlignCenter().Text(text
                    //=>
                    //                    {
                    //                        text.CurrentPageNumber();
                    //                        text.Span(" / ");
                    //                        text.TotalPages();
                    //                    });
                    //             page.Background()
                    // .PaddingVertical(verticalMargin, Unit.Inch)
                    // .RotateRight()
                    // .Decoration(decoration =>
                    // {
                    //     decoration.Before().RotateRight().RotateRight().Element(DrawSide);
                    //     decoration.Content().Extend();
                    //     decoration.After().Element(DrawSide);

                    //     void DrawSide(IContainer container)
                    //     {
                    //         container
                    //             .Height(horizontalMargin, Unit.Inch)
                    //             .AlignMiddle()
                    //             .Row(row =>
                    //             {
                    //                 row.AutoItem().PaddingRight(12).Text("LEGALLOK").FontSize(14).FontColor("#000000");
                    //                 row.RelativeItem().PaddingTop(8).ExtendHorizontal().LineHorizontal(2).LineColor("#FF9933");
                    //             });
                    //     }
                    // });
                });
        }
        void ComposeHeader(IContainer container)
        {

            container.Row(row =>
                       {
                           row.RelativeItem().Column(column =>
                           {
                               //    var stream = Assembly.GetExecutingAssembly().GetManifestResourceStream("Factory.CcaAvenueInvoicePlugin.Logo.png");
                               //    if (stream != null)
                               //    {

                               column.Item().Row(row =>
                               {
                                   row.RelativeItem(1)
                                           .Height(50)
                                     .Width(101)
                                       .Image(_logo);
                                   row.RelativeItem(4);
                               });
                               //   }

                               column.Item().PaddingTop(35).Text(text =>
                                                     {
                                                         text.Span($"INVOICE#:").FontFamily("Liberation Sans").FontSize(12).Bold();
                                                         text.Span($"{_model.InvoiceNumber}").FontFamily("Liberation Sans").FontSize(12).Bold();
                                                     });

                               DateTime date = DateTime.ParseExact(_model.IssueDate.Date.ToString("dd-MM-yyyy"), "dd-MM-yyyy", null);
                               int day = date.Day;
                               string ordinalIndicator = GetOrdinalIndicator(day);
                               string formattedDate = date.ToString("MMM d") + ordinalIndicator + date.ToString(" yyyy");
                               column.Item().Text(text =>
                               {
                                   text.Span("DATE ISSUED : ").FontSize(10);
                                   text.Span($"{formattedDate}").FontSize(10);
                               });
                           });

                           //    row.ConstantItem(175).Image(LogoImage);
                       });
        }



        static string GetOrdinalIndicator(int day)
        {
            if (day >= 11 && day <= 13)
            {
                return "th";
            }

            switch (day % 10)
            {
                case 1:
                    return "st";
                case 2:
                    return "nd";
                case 3:
                    return "rd";
                default:
                    return "th";
            }
        }
        void ComposeContent(IContainer container)
        {
            container.PaddingVertical(60).Column(column =>
                        {
                            column.Spacing(20);

                            column.Item().Element(ComposeTable);

                            // column.Item().AlignRight().Text(text =>
                            //   {
                            //       text.Span("Amount Paid :").SemiBold();
                            //       text.Span($"{_model.Total}");
                            //   });

                            // column.Item().Text(text =>
                            //                             {
                            //                                 text.Span("Payment Status :").SemiBold();
                            //                                 text.Span($"{_model.Status}").FontColor(Colors.Green.Medium);
                            //                             });

                            // column.Item().PaddingTop(10).Element(ComposeComments);
                        });
            void ComposeTable(IContainer container)
            {
                double amount = Convert.ToDouble(_model.Total);
                // double gstPercentage = 18;
                double subtotal = (amount / (1 + (_model.Gst / 100)));
                string formattedSubtotal = subtotal.ToString("F2");
                var headerStyle = TextStyle.Default;

                container.Table(table =>
                {
                    table.ColumnsDefinition(columns =>
                    {
                        columns.RelativeColumn();
                        // columns.ConstantColumn(75);
                        // columns.RelativeColumn();
                        // columns.RelativeColumn();
                        // columns.RelativeColumn();
                        // columns.RelativeColumn();
                        columns.ConstantColumn(110);
                        columns.ConstantColumn(25);
                        // columns.ConstantColumn(75);
                        columns.ConstantColumn(75);
                        columns.ConstantColumn(75);
                        // columns.RelativeColumn();
                        //  columns.RelativeColumn();
                    });

                    table.Header(header =>
                                  {
                                      header.Cell().Text("CUSTOMER CONTACT").FontSize(12).FontFamily("Liberation Sans").Bold();
                                      header.Cell().Text("DESCRIPTION").FontSize(12).FontFamily("Liberation Sans").Bold();
                                      header.Cell().AlignRight().Text("QTY").FontSize(12).FontFamily("Liberation Sans").Bold();
                                      header.Cell().AlignRight().Text("PRICE").FontSize(12).FontFamily("Liberation Sans").Bold();
                                      header.Cell().AlignRight().Text("TOTAL").FontSize(12).FontFamily("Liberation Sans").Bold();


                                      header.Cell().ColumnSpan(5).PaddingTop(5).BorderBottom(1).BorderColor(Colors.Black);
                                  });

                    table.Cell().Element(CellStyle).Text(text =>
                    {
                        text.Line($"{_model.BillingName}").FontSize(10);
                        text.Line($"{_model.BillingAddress}").FontSize(10);
                        text.Line($"{_model.BillingCity}").FontSize(10);
                        text.Line($"{_model.BillingState}-{_model.BillingZip}").FontSize(10);
                        text.Line($"Mobile :{_model.Mobile}").FontSize(10);
                        text.Line($"Email:{_model.BillingEmail}").FontSize(10);
                    });
                    table.Cell().Element(CellStyle).Text($"{_model.Description}").FontSize(10);
                    table.Cell().Element(CellStyle).PaddingLeft(10).Text($"{_model.Quantity}").FontSize(10);
                    table.Cell().Element(CellStyle).AlignRight().PaddingRight(5).Text($"{formattedSubtotal}").FontSize(10);
                    table.Cell().Element(CellStyle).AlignRight().PaddingRight(5).Text($"{formattedSubtotal}").FontSize(10);



                    static IContainer CellStyle(IContainer container) => container.BorderBottom(1).BorderColor(Colors.Grey.Lighten2).PaddingVertical(10);


                    //  }
                });
            }
            // void ComposeComments(IContainer container)
            // {
            //     container.ShowEntire().Background(Colors.Grey.Lighten3).Column(column =>
            //     {
            //         column.Spacing(5);
            //         column.Item().Text("Note:").FontSize(8).SemiBold();
            //         column.Item().Text(_model.Comments).FontSize(8);
            //     });
            // }
        }


        void ComposeFooter(IContainer container)
        {
            double amount = Convert.ToDouble(_model.Total);
            // double gstPercentage = 18;
            double subtotal = (amount / (1 + (_model.Gst / 100)));
            string formattedSubtotal = subtotal.ToString("F2");
            string gst = (amount - subtotal).ToString("F2");
            container.AlignLeft().PaddingBottom(40).Column(column =>
           {
               column.Item().Row(row =>
                            {
                                row.RelativeItem(1)
                               .Text(text =>
                                {
                                    text.Line("PAYABLE TO")
                                    .FontSize(10);
                                    text.Line("Legal Lok")
                                   .FontSize(8);
                                    text.Line("Reg.Name: Office Diary Pvt. Ltd.")
                                   .FontSize(8);
                                    text.Line("WeWork, Raheja Woods, Kalyani Nagar")
                                    .FontSize(8);
                                    text.Line("Pune, Maharashtra – 411006")
                                   .FontSize(8);
                                    text.Line("Email: <EMAIL>")
                                    .FontSize(8);
                                    text.Span("Website:")
                                   .FontSize(8);
                                    text.Line("www.legallok.com")
                    .FontSize(8).FontColor("#0000FF").Underline();
                                    text.Line("GSTIN 27AADCO4882C1ZF")
                                  .FontSize(9);
                                });
                                row.RelativeItem(2)
                              .BorderTop(1)
                              .Text(text =>
                              {
                                  text.Span("SubTotal")
                                      .FontSize(10);
                                  var alignmentSpace = new string(' ', 100);
                                  text.Span(alignmentSpace + $"{formattedSubtotal}")
                                      .FontSize(12);

                                  string numberString = _model.Total;
                                  int decimalIndex = numberString.IndexOf('.');
                                  string digitsBeforeDecimal = decimalIndex >= 0 ? numberString.Substring(0, decimalIndex) : numberString;
                                  int countDigitsBeforeDecimal = digitsBeforeDecimal.Count(char.IsDigit);
                                  if (countDigitsBeforeDecimal <= 3)
                                  {
                                      var alignmentSpace1 = new string(' ', 170);
                                      text.Span(alignmentSpace1);
                                      text.Span($"GST {_model.Gst}%")
                                        .FontSize(10);
                                      var alignmentSpace4 = new string(' ', 101);
                                      text.Span(alignmentSpace4 + gst)
                                          .FontSize(12);
                                      text.Span(alignmentSpace1);
                                      text.Span("Total")
                                        .FontSize(14).FontFamily("Liberation Sans").Bold();
                                      var alignmentSpace2 = new string(' ', 55);
                                      text.Span(alignmentSpace2 + $"Rs.{_model.Total}")
                                          .FontSize(14).FontFamily("Liberation Sans").Bold();
                                  }
                                  else
                                  {
                                      var alignmentSpace1 = new string(' ', 167);
                                      text.Span(alignmentSpace1);
                                      text.Span($"GST {_model.Gst}%")
                                        .FontSize(10);
                                      var alignmentSpace4 = new string(' ', 102);
                                      text.Span(alignmentSpace4 + gst)
                                          .FontSize(12);
                                      alignmentSpace1 = new string(' ', 160);
                                      text.Span(alignmentSpace1);
                                      text.Span("Total")
                                        .FontSize(14).FontFamily("Liberation Sans").Bold();
                                      var alignmentSpace2 = new string(' ', 54);
                                      text.Span(alignmentSpace2 + $"Rs.{_model.Total}")
                                          .FontSize(14).FontFamily("Liberation Sans").Bold();

                                  }






                              });

                                // row.RelativeItem(2)
                                //  .BorderTop(1)
                                //  .Text(text =>
                                //  {
                                //      text.Span("TOTAL")
                                //          .FontSize(12).Bold();
                                //      var alignmentSpace = new string(' ', 80);
                                //      text.Span(alignmentSpace + $"Rs.{_model.Total}").Bold()
                                //          .FontSize(12);
                                //  });
                            });
           });
        }
    }

    public static class Mapper
    {
        public static T Map<T>(ExpandoObject source) where T : class
        {
            T destination = (T)Activator.CreateInstance(typeof(T));
            IDictionary<string, object> dict = source;
            var type = destination.GetType();

            foreach (var prop in type.GetProperties())
            {
                var lower = prop.Name.ToLower();
                Console.WriteLine($"Mapping {lower}");
                var key = dict.Keys.SingleOrDefault(k => k.ToLower() == lower);

                if (key != null)
                {
                    prop.SetValue(destination, CastProperty(dict[key], prop.PropertyType.ToString()), null);
                }
            }
            return destination;
        }

        static dynamic CastProperty(dynamic property, string type)
        {
            switch (type)
            {
                case "System.String":
                    return (String)property;
                case "System.Decimal":
                    return (Decimal)property;
                default:
                    return property;

            }
        }
        public class Model
        {
            public string InvoiceNumber { get; set; }
            public DateTime IssueDate { get; set; } = TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, TimeZoneInfo.FindSystemTimeZoneById("Asia/Kolkata")).Date;
            public string ContactInfo { get; set; }
            public double Gst { get; set; }
            public string BillingName { get; set; }
            public string BillingAddress { get; set; }
            public string BillingCity { get; set; }
            public string BillingState { get; set; }
            public string BillingZip { get; set; }
            public string BillingEmail { get; set; }
            public string Mobile { get; set; }
            public string Total { get; set; }
            // public string CardType { get; set; }
            // public string Status { get; set; }
            public string Price { get; set; }
            public string Description { get; set; }
            public string Quantity { get; set; } = "1";
            // public string Comments { get; set; }
        }

    }
}
