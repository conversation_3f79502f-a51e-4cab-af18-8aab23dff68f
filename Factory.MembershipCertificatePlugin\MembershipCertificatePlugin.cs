using QuestPDF.Fluent;
using QuestPDF.Infrastructure;
using QuestPDF.Drawing;
using Svg.Skia;
using Factory.Plugins;
using Factory.Models;
using System.Reflection;
using System.Dynamic;
using QuestPDF.Helpers;


namespace Factory.MembershipCertificatePlugin
{
    public class MembershipCertificatePlugin : IRemoteCallPlugin
    {
        public string GetName()
        {
            return "MembershipCertificate";
        }

        public List<string> GetMethods()
        {
            return new List<string>() {
                "Generate"
            };
        }

        public string GetHelp()
        {
            return "";
        }

        public List<FieldMeta> GetConfigurationMeta()
        {
            return new List<FieldMeta>() {
            new FieldMeta()
            {
                Name = "Logo",
                Category = FieldCategory.Primitive,
                IsCollection = false,
                IsRequired = true,
                IsUnique = false,
                FileExtension = ".",
                StorageType = StorageType.Plain,
                Type = PrimitiveType.Text.ToString(),
        }
    };
        }

        private byte[] _logo { get; set; }
        public void Configure(dynamic configuration)
        {
            // var logoBase64String = ((IDictionary<string, object>)configuration)["Logo"].ToString();
            // _logo = Convert.FromBase64String(logoBase64String);
        }

        public Task<dynamic> Invoke(dynamic input)
        {
            try
            {
                dynamic output = new ExpandoObject();
                output.Result = new MembershipCertificate(Mapper.Map<Model>(input), _logo).GeneratePdf();
                return Task.FromResult<dynamic>(output);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"===========Exception=====================>{ex.Message}");
                throw new Exception();

            }
        }


    }

    public class MembershipCertificate : IDocument
    {
        static MembershipCertificate()
        {
            var mangalFont = Assembly.GetExecutingAssembly().GetManifestResourceStream("Factory.MembershipCertificatePlugin.timesnewromanps-boldmt.ttf");
            var mangalFont1 = Assembly.GetExecutingAssembly().GetManifestResourceStream("Factory.MembershipCertificatePlugin.TimesNewRomanPS BoldMT.ttf");
            if (mangalFont != null)
            {
                FontManager.RegisterFont(mangalFont);
                FontManager.RegisterFont(mangalFont1);
            }
            var timesFont = Assembly.GetExecutingAssembly().GetManifestResourceStream("Factory.MembershipCertificatePlugin.vijayab.ttf");
            if (timesFont != null)
            {
                FontManager.RegisterFont(timesFont);
            }
            var calibri = Assembly.GetExecutingAssembly().GetManifestResourceStream("Factory.MembershipCertificatePlugin.Arial-MT-Bold.ttf");
            if (calibri != null)
            {
                FontManager.RegisterFont(calibri);
            }
        }

        private byte[] _logo;
        public Model _model { get; }

        public MembershipCertificate(Model model, byte[] logo)
        {
            _model = model;
            _logo = logo;
        }

        public DocumentMetadata GetMetadata() => DocumentMetadata.Default;

        public void Compose(IDocumentContainer container)
        {
            container.Page(page =>
            {
                //page.Width = 297;
                // page.Height = 210;
                page.Margin(25);
                page.Size(PageSizes.Letter.Landscape());
                // page.Size = new Size(297, 210);
                Console.WriteLine($"==================NAMEMODEL{_model.FirmName}");
                Console.WriteLine($"==================NAMEM{_model.CertificateNumber}");
                switch (_model.Type.ToString())
                {
                    case "Rera":
                        var stream3 = Assembly.GetExecutingAssembly().GetManifestResourceStream("Factory.MembershipCertificatePlugin.Background3.png");
                        if (stream3 != null)
                        {
                            page.Background()
                                .Image(new BinaryReader(stream3).ReadBytes((int)stream3.Length), ImageScaling.Resize);
                        }
                        page.Content().Element(ComposeContentRera);
                        break;
                    case "Associate":
                        var stream2 = Assembly.GetExecutingAssembly().GetManifestResourceStream("Factory.MembershipCertificatePlugin.Background2.png");
                        if (stream2 != null)
                        {
                            page.Background()
                                .Image(new BinaryReader(stream2).ReadBytes((int)stream2.Length), ImageScaling.Resize);
                        }
                        page.Content().Element(ComposeContentAssociate);
                        break;
                    case "Ordinary":
                        var stream = Assembly.GetExecutingAssembly().GetManifestResourceStream("Factory.MembershipCertificatePlugin.Background.png");
                        if (stream != null)
                        {
                            page.Background()
                                .Image(new BinaryReader(stream).ReadBytes((int)stream.Length), ImageScaling.Resize);
                        }
                        page.Content().Element(ComposeContent);
                        break;
                    default:
                        break;
                }
            });
        }




        void ComposeContentRera(IContainer container)
        {
            container
          .Column(column =>
                      {
                          column.Item().PaddingTop(10).AlignRight().PaddingRight(110).Text(text =>
    {
        text.Span($"{_model.CertificateNumber}").FontSize(14).FontFamily("Arial MT").Bold();

    });


                          column.Item().PaddingTop(150).AlignCenter().Text(text =>
                               {
                                   text.Span("").FontColor("#B22236")

                                       .FontSize(30).FontFamily("Arial MT").Bold();
                               });
                          column.Item().AlignCenter().Text(text =>
                 {
                     text.Span("This is to Certify that")

                         .FontSize(15).FontFamily("Arial MT").Bold();
                 });
                          column.Item().AlignCenter().Text(text =>
 {
     string name = _model.ProjectName;
     int fontSize = fontSize = name.Length < 20 ? 38 :
          (name.Length > 90 ? 15 :
          (name.Length > 59 ? 20 :
          (name.Length > 53 ? 25 :
          (name.Length > 42 ? 28 : 35)))); ;
     if (IsAllUpper(name) && name.Length > 18)
     {
         fontSize = fontSize = name.Length < 20 ? 38 :
                    (name.Length > 45 ? 20 :
                    (name.Length > 35 ? 25 :
                    (name.Length > 25 ? 28 :
                    (name.Length > 18 ? 30 : 28)))); ;
     }

     text.Span(name).FontSize(fontSize).FontColor("#B22236").FontFamily("Vijaya").Bold();
 });
                          column.Item().AlignCenter().Text(text =>
                          {
                              text.Span($"Project of")
 .FontSize(15).FontFamily("Arial MT").Bold(); ;
                          });
                          column.Item().AlignCenter().Text(text =>
                   {

                       string name = _model.FirmName;
                       int fontSize = fontSize = name.Length < 20 ? 38 :
                            (name.Length > 90 ? 15 :
                            (name.Length > 59 ? 20 :
                            (name.Length > 53 ? 25 :
                            (name.Length > 42 ? 28 : 35)))); ;
                       if (IsAllUpper(name) && name.Length > 18)
                       {
                           fontSize = fontSize = name.Length < 20 ? 38 :
                                      (name.Length > 45 ? 20 :
                                      (name.Length > 35 ? 25 :
                                      (name.Length > 25 ? 28 :
                                      (name.Length > 18 ? 30 : 28)))); ;
                       }

                       text.Span($"{_model.FirmName}").FontSize(fontSize).FontColor("#B22236").FontFamily("Vijaya")

                           .Bold();
                   });
                          column.Item().AlignCenter().Text(text =>
                     {
                         text.Span($"has been Awarded RERA Project Membership of this Association under the ")

                           .FontSize(14).FontFamily("Arial MT").Bold();
                     });
                          column.Item().AlignCenter().Text(text =>
                        {
                            DateTime date = DateTime.Now;
                            string formattedDate = date.ToString("dd-MM-yyyy");
                            text.Span($"Authority of the Managing Committee from the date {GenerateDateSuffix(formattedDate)} ")

                                .FontSize(14).FontFamily("Arial MT").Bold(); ;
                        });
                          column.Item().AlignCenter().PaddingTop(10).Text(text =>
                          {
                              text.Line($"Validity till {GenerateDateSuffix(_model.ToDate)}")

                                  .FontSize(15).FontFamily("Arial MT")
                                  .Bold();
                              text.Line("(date of Completion of the Project)")
                            .FontSize(12).FontFamily("Arial MT")
                            .Bold();


                          });
                          column.Item().Row(row =>
                               {
                                   row.RelativeItem(1)
                       .PaddingLeft(85)
                       .Width(4 * 40)
                       .Height(3 * 30)
                       .Image(new BinaryReader(_model.PresidentSignature).ReadBytes((int)_model.PresidentSignature.Length), ImageScaling.Resize);
                                   row.RelativeItem(1);
                                   row.RelativeItem(1)
                        .Width(4 * 40)
                        .Height(3 * 30)
                        .Image(new BinaryReader(_model.SecaratorySignature).ReadBytes((int)_model.SecaratorySignature.Length), ImageScaling.Resize);
                               });
                      });
        }























        void ComposeContentAssociate(IContainer container)
        {
            container
            .Column(column =>
                        {
                            column.Item().PaddingTop(40).AlignRight().PaddingRight(90).Text(text =>
      {
          text.Span($"{_model.CertificateNumber}").FontSize(14).FontFamily("Arial MT");

      });

                            column.Item().PaddingTop(220).AlignCenter().Text(text =>
               {
                   text.Span("This is to Certify that")
                   .FontSize(15)
                   .FontFamily("Arial MT")

                   .Bold();
               });
                            column.Item().AlignCenter().Text(text =>
{
    string name = _model.ProjectName;
    int fontSize = fontSize = name.Length < 20 ? 38 :
         (name.Length > 90 ? 15 :
         (name.Length > 59 ? 20 :
         (name.Length > 53 ? 25 :
         (name.Length > 42 ? 28 : 35)))); ;
    if (IsAllUpper(name) && name.Length > 35)
    {
        fontSize = fontSize = name.Length < 20 ? 38 :
                   (name.Length > 90 ? 10 :
                   (name.Length > 59 ? 15 :
                   (name.Length > 53 ? 20 :
                   (name.Length > 42 ? 25 : 28)))); ;
    }
    text.Span(name).FontSize(fontSize).FontColor("#b30000").FontFamily("Vijaya").Bold();
});
                            column.Item().AlignCenter().Text(text =>
                            {
                                text.Span($"is Associate Member of this Association.")

                                    .FontSize(15)
                                       .FontFamily("Arial MT")
                                    .Bold();
                            });
                            column.Item().AlignCenter().Text(text =>
                            {
                                text.Span($"Awarded under the Authority of this Association")

                                    .FontSize(15)
                                       .FontFamily("Arial MT")
                                    .Bold();
                            });
                            column.Item().AlignCenter().PaddingTop(10).Text(text =>
                 {
                     if (_model.Renewal)
                     {
                         if (DateTime.Now.Month != 1)
                         {
                             text.Span($"Validity: 1st April {DateTime.Now.Year} to 31st March {DateTime.Now.AddYears(1).Year}")
                                 .FontSize(15)
                                 .FontFamily("Arial MT")
                                 .Bold();
                         }
                         else
                         {
                             text.Span($"Validity: 1st April {DateTime.Now.Year} to 31st March {DateTime.Now.Year}")
                                 .FontSize(15)
                                 .FontFamily("Arial MT")
                                 .Bold();
                         }

                     }
                     else
                     {
                         text.Span($"Validity: {GenerateDateSuffix(_model.FromDate)} to 31st March {_model.ToYear}")

                                  .FontSize(15)
                                  .FontFamily("Arial MT")
                                  .Bold();
                     }
                 }



                 );

                            column.Item().PaddingTop(20).Row(row =>
                               {
                                   row.RelativeItem(1)
                       .PaddingLeft(85)
                       .Width(4 * 40)
                       .Height(3 * 30)
                       .Image(new BinaryReader(_model.PresidentSignature).ReadBytes((int)_model.PresidentSignature.Length), ImageScaling.Resize);
                                   row.RelativeItem(1);
                                   row.RelativeItem(1)
                        .Width(4 * 40)
                        .Height(3 * 30)
                        .Image(new BinaryReader(_model.SecaratorySignature).ReadBytes((int)_model.SecaratorySignature.Length), ImageScaling.Resize);
                               });

                        });
        }
























        void ComposeContent(IContainer container)
        {
            container.Column(column =>
                       {

                           column.Item().PaddingTop(40).AlignRight().PaddingRight(120).Text(text =>
                            {
                                text.Span($"{_model.CertificateNumber}").FontSize(14).FontFamily("Arial MT");
                            });

                           column.Item().PaddingTop(220).AlignCenter().Text(text =>
               {
                   text.Span("This is to Certify that")

                   .FontSize(15)
                   .FontFamily("Arial MT")

                   .Bold();
               });
                           column.Item().AlignCenter().Text(text =>
  {
      string name = _model.ProjectName;
      int fontSize = fontSize = name.Length < 20 ? 38 :
           (name.Length > 90 ? 15 :
           (name.Length > 59 ? 20 :
           (name.Length > 53 ? 25 :
           (name.Length > 42 ? 28 : 35)))); ;
      if (IsAllUpper(name) && name.Length > 35)
      {
          fontSize = fontSize = name.Length < 20 ? 38 :
                     (name.Length > 90 ? 10 :
                     (name.Length > 59 ? 15 :
                     (name.Length > 53 ? 20 :
                     (name.Length > 42 ? 25 : 28)))); ;
      }

      text.Span(name).FontSize(fontSize).FontColor("#b30000").FontFamily("Vijaya").Bold();
  });
                           column.Item().AlignCenter().Text(text =>
                {
                    text.Span($"is a Member of this Association.")

                            .FontSize(15)
                            .FontFamily("Arial MT")
                            .Bold();
                });
                           column.Item().AlignCenter().Text(text =>
                {
                    text.Span($"Awarded under the Authority of this Association")

                            .FontSize(15)
                            .FontFamily("Arial MT")
                            .Bold();
                });

                           column.Item().AlignCenter().PaddingTop(10).Text(text =>
                {
                    if (_model.Renewal)
                    {
                        if (DateTime.Now.Month != 1)
                        {
                            text.Span($"Validity: 1st April {DateTime.Now.Year} to 31st March {DateTime.Now.AddYears(1).Year}")
                                .FontSize(15)
                                .FontFamily("Arial MT")
                                .Bold();
                        }
                        else
                        {
                            text.Span($"Validity: 1st April {DateTime.Now.Year} to 31st March {DateTime.Now.Year}")
                                .FontSize(15)
                                .FontFamily("Arial MT")
                                .Bold();
                        }
                    }
                    else
                    {
                        text.Span($"Validity: {GenerateDateSuffix(_model.FromDate)} to 31st March {_model.ToYear}")

                                 .FontSize(15)
                                 .FontFamily("Arial MT")
                                 .Bold();
                    }

                });

                           column.Item().PaddingTop(20).Row(row =>
                               {
                                   row.RelativeItem(1)
                       .PaddingLeft(85)
                       .Width(4 * 40)
                       .Height(3 * 30)
                       .Image(new BinaryReader(_model.PresidentSignature).ReadBytes((int)_model.PresidentSignature.Length), ImageScaling.Resize);
                                   row.RelativeItem(1);
                                   row.RelativeItem(1)
                        // .PaddingRight(70)
                        .Width(4 * 40)
                        .Height(3 * 30)
                        .Image(new BinaryReader(_model.SecaratorySignature).ReadBytes((int)_model.SecaratorySignature.Length), ImageScaling.Resize);
                               });
                       });
        }



        string GenerateDateSuffix(string dateStr)
        {
            DateTime date = DateTime.ParseExact(dateStr, "dd-MM-yyyy", null);
            string result1 = $"{date.Day}{GetOrdinalSuffix(date.Day)} {date:MMMM yyyy}";
            return result1;
        }
        bool IsAllUpper(string input)
        {
            for (int i = 0; i < input.Length; i++)
            {
                if (!Char.IsUpper(input[i]) && !Char.IsWhiteSpace(input[i]))
                {
                    return false;
                }
            }
            return true;
        }


        string GetOrdinalSuffix(int day)
        {
            if (day >= 11 && day <= 13)
            {
                return "th";
            }

            switch (day % 10)
            {
                case 1:
                    return "st";
                case 2:
                    return "nd";
                case 3:
                    return "rd";
                default:
                    return "th";
            }
        }

    }

    public static class Mapper
    {
        public static T Map<T>(ExpandoObject source) where T : class
        {
            T destination = (T)Activator.CreateInstance(typeof(T));
            IDictionary<string, object> dict = source;
            var type = destination.GetType();

            foreach (var prop in type.GetProperties())
            {
                var lower = prop.Name.ToLower();
                Console.WriteLine($"Mapping {lower}");
                var key = dict.Keys.SingleOrDefault(k => k.ToLower() == lower);

                if (key != null)
                {
                    prop.SetValue(destination, CastProperty(dict[key], prop.PropertyType.ToString()), null);
                }
            }
            return destination;
        }

        static dynamic CastProperty(dynamic property, string type)
        {
            switch (type)
            {
                case "System.String":
                    return (String)property;
                case "System.Decimal":
                    return (Decimal)property;
                default:
                    return property;

            }
        }

    }


    public class Model
    {
        public string CertificateNumber { get; set; }
        public string ReraValiditydate { get; set; }
        public dynamic PresidentSignature { get; set; }
        public dynamic SecaratorySignature { get; set; }
        public string Reradate { get; set; }
        public bool Renewal { get; set; }
        public string Type { get; set; }
        public string ToYear { get; set; }
        public string FromDate { get; set; }
        public string ToDate { get; set; }
        public string President { get; set; }
        public string Secretary { get; set; }
        // public byte[] Watermark { get; set; }
        // public byte[] Logo { get; set; }
        public string FirmName { get; set; }
        public string ProjectName { get; set; }
        // public string Address { get; set; }
        // public DateTime FromDate { get; set; }
        // public int ToYear { get; set; }
    }
}
