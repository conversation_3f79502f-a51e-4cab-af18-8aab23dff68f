using System.Collections.Generic;
using System.Net;
using System.Net.Mail;
using Factory.Plugins;
using Factory.Models;

namespace Factory.Plugins {

    public class SampleRemoteCallPlugin : IRemoteCallPlugin
    {
        public string GetName() {
            return "";
        }
        public List<FieldMeta> GetConfigurationMeta()
        {
            return new List<FieldMeta>() {
            };
        }

        public void Configure(dynamic configuration) {
        }

        public Task<dynamic> Invoke(dynamic input)
        {
            return Task.FromResult<dynamic>(null);
        }

        public List<string> GetMethods()
        {
            throw new NotImplementedException();
        }

        public string GetHelp()
        {
            throw new NotImplementedException();
        }
    }
}