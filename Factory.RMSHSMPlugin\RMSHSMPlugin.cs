using System.Collections.Generic;
using System.Dynamic;
using System.Net;
using System.Text;
using Factory.Plugins;
using Factory.Models;

namespace Factory.RMSHSMPlugin {

    public class RMSHSMPlugin : IRemoteCallPlugin
    {
        public void Configure(dynamic configuration)
        {
        }

        public List<FieldMeta> GetConfigurationMeta()
        {
            return new List<FieldMeta>();
        }

        public string GetHelp()
        {
            return "";
        }

        public List<string> GetMethods()
        {
            return new List<string>();
        }

        public string GetName()
        {
            return "RMS HSM Signer";
        }

        public async Task<dynamic> Invoke(dynamic input)
        {
            dynamic output = new ExpandoObject();
                
            var model = Mapper.Map<Model>(input);
            var client = new ServiceReference.DSVerifyWSClient(new ServiceReference.DSVerifyWSClient.EndpointConfiguration());
            var inputData = Convert.ToBase64String(model.File);
            var result = await client.signPdfAsync(model.Transaction, model.KeyLabel, inputData, "", model.Coordinates, 
                "last", "", "", model.CoSign.ToString(), model.OTP, model.OTPType, "", "");
            
            Type type = result.GetType();

            foreach (var f in type.GetFields().Where(f => f.IsPublic)) {
                if (f.Name == "return") {
                    var resultTokens = f.GetValue(result).Split("~");
                    if (resultTokens[1] == "SUCCESS") {
                        output.Result = Convert.FromBase64String(resultTokens[2]); 
                    }
                }
            }                           
            return output; 
        }
    }

    public static class Mapper
    {
        public static T Map<T>(ExpandoObject source) where T : class
        {
            T destination = (T)Activator.CreateInstance(typeof(T));
            IDictionary<string, object> dict = source;
            var type = destination.GetType();

            foreach (var prop in type.GetProperties())
            {
                var lower = prop.Name.ToLower();
                Console.WriteLine($"Mapping {lower}");
                var key = dict.Keys.SingleOrDefault(k => k.ToLower() == lower);

                if (key != null)
                {
                    prop.SetValue(destination, CastProperty(dict[key], prop.PropertyType.ToString()), null);
                }
            }
            return destination;
        }

        static dynamic CastProperty(dynamic property, string type)
        {
            switch (type)
            {
                case "System.String":
                    return (String)property;
                case "System.Decimal":
                    return (Decimal)property;
                default:
                    return property;

            }
        }

    }

    public class Model
    {
        public string Transaction { get; set; }
        public string KeyLabel { get; set; }
        public byte[] File { get; set; }
        public String Coordinates { get; set; }
        public String OTP { get; set; }
        public String OTPType { get; set; }
        public bool CoSign { get; set; }
    }
}