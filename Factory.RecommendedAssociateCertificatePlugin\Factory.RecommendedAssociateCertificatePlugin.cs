using QuestPDF.Fluent;
using QuestPDF.Infrastructure;
using QuestPDF.Drawing;
using Svg.Skia;
using Factory.Plugins;
using Factory.Models;
using System.Reflection;
using System.Dynamic;
using QuestPDF.Helpers;
using SkiaSharp;
using static Factory.RecommendedAssociateCertificatePlugin.Mapper;

namespace Factory.RecommendedAssociateCertificatePlugin
{
  public class RecommendedAssociateCertificatePlugin : IRemoteCallPlugin
  {
    public string GetName()
    {
      return "RecommendedAssociateCertificate";
    }

    public List<string> GetMethods()
    {
      return new List<string>() {
                "Generate"
            };
    }

    public string GetHelp()
    {
      return "";
    }

    public List<FieldMeta> GetConfigurationMeta()
    {
      return new List<FieldMeta>() { };
    }



    public void Configure(dynamic configuration)
    {
    }

    public Task<dynamic> Invoke(dynamic input)
    {

      dynamic output = new ExpandoObject();
      output.Result = new RecommendedAssociateCertificate(Mapper.Map<Model>(input)).GeneratePdf();
      return Task.FromResult<dynamic>(output);
    }
  }

  public class RecommendedAssociateCertificate : IDocument
  {
    static RecommendedAssociateCertificate()
    {
      var timesFont = Assembly.GetExecutingAssembly().GetManifestResourceStream("Factory.RecommendedAssociateCertificatePlugin.cambriab.ttf");
      if (timesFont != null)
      {
        FontManager.RegisterFont(timesFont);
      }
      var times = Assembly.GetExecutingAssembly().GetManifestResourceStream("Factory.RecommendedAssociateCertificatePlugin.Cambria.ttf");
      if (times != null)
      {
        FontManager.RegisterFont(times);
      }

    }




    public Model _model { get; }

    public RecommendedAssociateCertificate(Model model)
    {
      _model = model;
    }


    public DocumentMetadata GetMetadata() => DocumentMetadata.Default;

    public void Compose(IDocumentContainer container)
    {
      container
          .Page(page =>
          {
            page.Size(new PageSize(10f, 14f, Unit.Inch));
            page.Margin(30f);
            switch (_model.Type)
            {
              case "Associate":

                // page.Header().Element(ComposeAssociateHeader);
                page.Content().Element(ComposeAssociateContent);
                break;
              case "Ordinary":

                // page.Header().Element(ComposeOrdinaryHeader);
                page.Content().Element(ComposeOrdinaryContent);
                break;
              case "Rera":

                // page.Header().Element(ComposeReraHeader);
                page.Content().Element(ComposeReraContent);
                break;
              default:
                break;

            }

            // page.Content().Element(ComposeTable);
            // page.Content().Element(ComposeAssociateContent2);


          });
    }
    void ComposeReraContent(IContainer container)
    {
      DateTime date = TimeZoneInfo.ConvertTimeFromUtc(_model.CreatedDate, TimeZoneInfo.FindSystemTimeZoneById("Asia/Kolkata")).Date;
      DateTime dateOfPayment = TimeZoneInfo.ConvertTimeFromUtc(_model.DateOfPayment, TimeZoneInfo.FindSystemTimeZoneById("Asia/Kolkata")).Date;

      container.Column(column =>
     {
       column.Item()
           .AlignLeft()
           .PaddingLeft(20)
           .PaddingRight(20)
              .Text(text =>
              {

                text.Span("Date: " + _model.Date.ToString("dd-MM-yyyy")).FontSize(10)
                          .FontFamily("Cambria");

              });
       column.Item()

          .PaddingLeft(20)
          .PaddingRight(20)
            .AlignLeft()
          .Text(text =>
          {

            text.Span("Office Note:").FontSize(10)
                      .FontFamily("Cambria");


          });
       column.Item()

          .PaddingLeft(20)
          .PaddingRight(20)
           .AlignLeft()
          .Text(text =>
          {

            text.Span($"{_model.DirectorGeneral}").FontSize(10)
                      .FontFamily("Cambria");

          });
       column.Item()

            .PaddingLeft(20)
            .PaddingRight(20)
            .AlignLeft()
            .Text(text =>
            {

              text.Span($"Director General{Environment.NewLine}").FontSize(10)
                        .FontFamily("Cambria");

            });

       column.Item()
            .PaddingLeft(20)
            .AlignLeft()
             .Text(text =>
             {

               text.Span("Sub: CREDAI-Pune ").FontSize(10)
                          .FontFamily("Cambria");
               text.Span("RERA Project	Membership Application ").FontSize(10)
                       .FontSize(10).Bold()
                       .FontFamily("Cambria");
               text.Span($"for approval.{Environment.NewLine}").FontSize(10)
                      .FontFamily("Cambria");
             });

       column.Item()
             .PaddingLeft(20)
             .PaddingRight(20)
             .AlignLeft()
             .Text(text =>
             {

               text.Span(@$"With reference to above, we are submitting herewith following application for RERA Project Membership along with the documents submitted by
them for your kind consideration. The details of the project applied are as below :").FontSize(10)
                     .FontFamily("Cambria");

             });
       column.Item()
            .MinimalBox()
            .PaddingLeft(20)
            .Border(1)
            .Table(table =>
  {
    IContainer DefaultCellStyle(IContainer container, string backgroundColor)
    {
      return container.Border(1f).PaddingHorizontal(1f).AlignLeft();
    }

    table.ColumnsDefinition(columns =>
        {
          columns.ConstantColumn(200f);
          columns.ConstantColumn(260f);
        });

    string[] source = _model.ProprietorOrPartnerNameOrDirectors.Split(',');
    string str = "";
    int count7 = 3;
    if (source.Length <= count7)
    {
      str = string.Join("\n", source);
    }
    else
    {
      for (int count8 = 0; count8 < source.Length; count8 += count7)
      {
        string[] array = ((IEnumerable<string>)source).Skip<string>(count8).Take<string>(count7).ToArray<string>();
        str = str + string.Join(", ", array) + "\n";
      }
    }
    //     table.Header(header =>
    //   {
    //       // please be sure to call the 'header' handler!

    //       header.Cell().ColumnSpan(1).Element(CellStyle).ExtendHorizontal().AlignLeft().Text("Name of Applicant Organization").FontSize(10).FontFamily("Cambria").Bold();
    //       header.Cell().ColumnSpan(1).Element(CellStyle).Text("Name of the Proprietor/Partner/Directors").FontSize(10).FontFamily("Cambria").Bold();
    //       header.Cell().ColumnSpan(1).Element(CellStyle).Text("Name of	the Proposer/Seconder").FontSize(10).FontFamily("Cambria").Bold();

    //       // you can extend existing styles by creating additional methods
    //       IContainer CellStyle(IContainer container) => DefaultCellStyle(container, Colors.Grey.Lighten3);
    //   });

    table.Header(header =>
          {
            // Call the 'header' handler

            // First row
            header.Cell().RowSpan(2).Element(CellStyle).AlignCenter().Text("Name of the Project").FontSize(10).FontFamily("Cambria").Bold();
            header.Cell().RowSpan(2).Element(CellStyle).AlignCenter().Text(_model.ProjectName).FontSize(10).FontFamily("Cambria");

            // Second row
            header.Cell().Element(CellStyle).AlignCenter().Text("Project address Location").FontSize(10).FontFamily("Cambria").Bold();
            header.Cell().Element(CellStyle).AlignCenter().Text(_model.Location).FontSize(10).FontFamily("Cambria");

            header.Cell().Element(CellStyle).AlignCenter().Text("Proposed date of Completion").FontSize(10).FontFamily("Cambria").Bold();
            header.Cell().Element(CellStyle).AlignCenter().Text(_model.DateOfCompletion).FontSize(10).FontFamily("Cambria");

            // Third row
            header.Cell().ColumnSpan(1).Element(CellStyle).Text("No. of units in Project").FontSize(10).FontFamily("Cambria").Bold();

            var unitsText = $"Residential: {_model.NoOfResidentailProject}, Commercial: {_model.NoOfCommercialProject}, Plot: {_model.NoOfPlotProject}";
            header.Cell().ColumnSpan(1).Element(CellStyle).Text(unitsText).FontSize(10).FontFamily("Cambria").Bold();

            // Fourth row
            header.Cell().ColumnSpan(1).Element(CellStyle).Text("Name of the Applicant Firm").FontSize(10).FontFamily("Cambria").Bold();
            header.Cell().Element(CellStyle).Text(_model.Firm ?? "").FontSize(10).FontFamily("Cambria");

            header.Cell().Element(CellStyle).Text("Address of the Firm").FontSize(10).FontFamily("Cambria").Bold();
            header.Cell().Element(CellStyle).Text(_model.FirmAddress ?? "").FontSize(10).FontFamily("Cambria");

            header.Cell().Element(CellStyle).Text("Name of the Partners/Proprietor/Directors/Members").FontSize(10).FontFamily("Cambria").Bold();
            header.Cell().Element(CellStyle).Text(str).FontSize(10).FontFamily("Cambria");
          });











    //     table.Cell().Element(CellStyle).Text(text =>

    //  {
    //      text.Line($"{_model.Firm}").FontSize(10).FontFamily("Cambria");
    //       text.Line($"").FontSize(10).FontFamily("Cambria");
    //      text.Span("Address:").FontSize(10).FontFamily("Cambria").Bold();
    //      text.Span($"{_model.Address}").FontSize(10).FontFamily("Cambria");
    //  });
    // ));

    // table.Cell().Element(CellStyle).Text($"M/s.{_model.OrdinaryMemberName}").FontSize(10).FontFamily("Cambria");

    //  table.Cell().Element(CellStyle).Text(text => 
    //  {
    //  text.Span("ProposarName: ").FontSize(9).FontFamily("Cambria").Bold();
    // text.Line(_model.ProposerName).FontSize(9).FontFamily("Cambria");
    // text.Span("ProposarFirmName: ").FontSize(9).FontFamily("Cambria").Bold();
    //             text.Line(_model.ProposerFirmName).FontSize(9).FontFamily("Cambria");
    //             text.Span("SeconderName: ").FontSize(9).FontFamily("Cambria").Bold();
    //             text.Line(_model.SecondaryName).FontSize(9).FontFamily("Cambria");
    //             text.Span("SeconderFirmName: ").FontSize(9).FontFamily("Cambria").Bold();
    //             text.Line(_model.SecondaryFirmName).FontSize(9).FontFamily("Cambria");



    //  });

















    IContainer CellStyle(IContainer container) => DefaultCellStyle(container, Colors.Grey.Lighten3);



    // IContainer CellStyle(IContainer container) => DefaultCellStyle(container, Colors.White).ShowOnce();
  });
       //    column.Item()
       //    .PaddingLeft(20)
       //    .PaddingRight(20)
       //   .AlignLeft()
       //    .Text(text =>
       //    {

       //      text.Line($"").FontSize(10)

       //             .FontFamily("Cambria");
       //        text.Span($"It has been confirmed from the records of the CREDAI-Pune Metro that as on today the above Proposer and Seconder do not have any outstanding dues and have paid Annual Subscription Charges for F.Y. 20{GetFinancialYear().Result.Split('-')[0]}-20{GetFinancialYear().Result.Split('-')[1]}.").FontSize(10)

       //             .FontFamily("Cambria");
       //              text.Line($"").FontSize(10)

       //             .FontFamily("Cambria");

       //    });
       column.Item()
            .PaddingLeft(20)
            .PaddingRight(20)
           .AlignLeft()
            .Text(text =>
            {
              text.Line($"").FontSize(10);
              text.Span($"The firm has submitted following documents along with the application:").FontSize(10)
                         .FontFamily("Cambria");
              text.Line($"").FontSize(10);
            });
       column.Item()
.PaddingLeft(20f)
           .MinimalBox()
           .Border(1)
           .Table(table =>
{
  IContainer DefaultCellStyle(IContainer container, string backgroundColor)
  {
    return container
             .Border(1)
             //  .BorderColor(Colors.Grey.Lighten1)
             //  .Background(backgroundColor)
             .PaddingHorizontal(1)
             .AlignLeft();




  }
  IContainer DefaultCellStyleForSno(IContainer container, string backgroundColor)
  {
    return container
             .Border(1)
             //  .BorderColor(Colors.Grey.Lighten1)
             //  .Background(backgroundColor)
             .PaddingHorizontal(1)
             .AlignCenter();




  }

  table.ColumnsDefinition(columns =>
     {
       columns.ConstantColumn(40f);
       columns.ConstantColumn(150f);
       columns.ConstantColumn(170f);
       columns.ConstantColumn(100f);



     });




  table.Header(header =>
     {
       // please be sure to call the 'header' handler!

       header.Cell().RowSpan(2).Element(CellStyle).ExtendHorizontal().AlignLeft().Text("");
       header.Cell().ColumnSpan(1).AlignCenter().Text("Document").FontSize(12).FontFamily("Cambria").FontSize(11).Bold();
       header.Cell().ColumnSpan(2).Element(CellStyle).AlignCenter().PaddingLeft(100f).Text("Remark").FontSize(12).FontFamily("Cambria").Bold();

       // you can extend existing styles by creating additional methods
       IContainer CellStyle(IContainer container) => DefaultCellStyle(container, Colors.Grey.Lighten3);
     });

  table.Cell().Row(1).Column(1).Element(CellStyle2).AlignLeft().Text("1.").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(2).Column(1).Element(CellStyle2).AlignLeft().Text("2.").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(7).Column(1).Element(CellStyle2).AlignLeft().Text("7.").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(10).Column(1).Element(CellStyle2).AlignLeft().Text("8.").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(1).Column(2).Element(CellStyle).ExtendHorizontal().AlignLeft().Text("PAN Card No. of firm").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(2).Column(2).Element(CellStyle).ExtendHorizontal().AlignLeft().Text("GST No. of firm").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(3).Column(2).Element(CellStyle).ExtendHorizontal().AlignLeft().Text("Copy of Aadhaar/PAN Card of the Proprietor/Partners/Directors/Members").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(4).Column(2).Element(CellStyle).ExtendHorizontal().AlignLeft().Text("Partnership Deed/MOA/Articles of Association/Shop Act Licence/AOP Agreement/LLP Agreements").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(5).Column(2).Element(CellStyle).ExtendHorizontal().AlignLeft().Text("Commencement/NA order").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(6).Column(2).Element(CellStyle).ExtendHorizontal().AlignLeft().Text("Copy of Code of Conduct ").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(7).Column(2).AlignLeft().Text("Payment Details").Bold().FontSize(10).FontFamily("Cambria");
  table.Cell().Row(8).Column(2).Element(CellStyle).ExtendHorizontal().AlignLeft().Text($"Amount:- Rs.{_model.Amt}/-").Bold().FontSize(10).FontFamily("Cambria");
  table.Cell().Row(9).Column(2).Element(CellStyle).ExtendHorizontal().AlignLeft().Text("R.No.").Bold().FontSize(10).FontFamily("Cambria");
  table.Cell().Row(10).Column(2).Element(CellStyle).ExtendHorizontal().AlignLeft().Text("ApplicationId & Date:").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(1).Column(3).Element(CellStyle).ExtendHorizontal().PaddingLeft(5).Text($"{_model.PanCardNumber}").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(2).Column(3).Element(CellStyle).ExtendHorizontal().PaddingLeft(5).Text($"{_model.GstNumber}").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(3).Column(3).Element(CellStyle).ExtendHorizontal().AlignCenter().Text($"Submitted").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(1).Column(4).Element(CellStyle).ExtendHorizontal().AlignLeft().Text("Verified").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(2).Column(4).Element(CellStyle).ExtendHorizontal().AlignLeft().Text("Verified").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(3).ColumnSpan(4).Element(CellStyle).ExtendHorizontal().AlignRight().Text("").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(4).ColumnSpan(4).Element(CellStyle).ExtendHorizontal().AlignRight().Text("").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(5).ColumnSpan(4).Element(CellStyle).ExtendHorizontal().AlignLeft().Text("").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(6).ColumnSpan(4).Element(CellStyle).ExtendHorizontal().AlignLeft().Text("").FontSize(10).FontFamily("Cambria");
  //  table.Cell().Row(3).ColumnSpan(4).Element(CellStyle).ExtendHorizontal().AlignCenter().PaddingRight(30f).Text("Submitted").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(4).Column(3).Element(CellStyle).ExtendHorizontal().AlignCenter().Text(_model.FileExists ? "Submitted" : "NotSubmitted").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(5).Column(3).Element(CellStyle).ExtendHorizontal().AlignCenter().Text(_model.CommencemenceFileExists ? "Submitted" : "NotSubmitted").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(6).Column(3).Element(CellStyle).ExtendHorizontal().AlignCenter().Text("Submitted").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(8).Column(3).Element(CellStyle).AlignLeft().Text("TransactionId:- " + _model.TransactionId).FontSize(10).FontFamily("Cambria");
  table.Cell().Row(8).Column(4).Element(CellStyle).AlignLeft().Text("Date :" + _model.DateOfPayment.ToString("dd-MM-yyyy")).FontSize(10).FontFamily("Cambria");
  table.Cell().RowSpan(2).ColumnSpan(2).Element(CellStyle).ExtendHorizontal().AlignLeft().Text(_model.ReceiptNumber).Bold().FontSize(10).FontFamily("Cambria");

  table.Cell().Row(3).Column(1).Element(CellStyle2).Text("3.").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(4).Column(1).Element(CellStyle2).Text("4.").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(5).Column(1).Element(CellStyle2).Text("5.").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(6).Column(1).Element(CellStyle2).Text("6.").FontSize(10).FontFamily("Cambria");

  table.Cell().Row(10).Column(3).Element(CellStyle).ExtendHorizontal().AlignLeft().Text(_model.ApplicationId).FontSize(10).FontFamily("Cambria");
  table.Cell().Row(10).Column(4).Element(CellStyle).ExtendHorizontal().AlignRight().Text("").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(10).Column(4).Element(CellStyle).ExtendHorizontal().AlignLeft().Text(date.ToString("dd-MM-yyyy")).FontSize(10).FontFamily("Cambria");

  IContainer CellStyle(IContainer container)
  {
    return DefaultCellStyle(container, "#ffffff").ShowOnce();
  }
});
















       column.Item()
             .PaddingVertical(20)
           .AlignLeft()
            .Text(text =>
            {

              text.Span($" Submitting the application for your Consideration & Recommendation").FontSize(10)
                         .FontSize(10).Bold()
                         .FontFamily("Cambria");
              text.Line($"").FontSize(10)
                         .FontSize(10).Bold()
                         .FontFamily("Cambria");

            });
       column.Item().Row(row =>
                                           {
                                             if (_model.IsScrutinizer || _model.VerifiedBySignature != null)
                                             {
                                               row.RelativeItem(1)
                                   .Width(2 * 20)
                                   .Height(2 * 20)
                                   .Image(new BinaryReader(_model.VerifiedBySignature).ReadBytes((int)_model.VerifiedBySignature.Length), ImageScaling.Resize);
                                             }
                                             if (_model.IsConvernor || _model.ConvernorSignature != null)
                                             {
                                               row.RelativeItem(1).AlignCenter()
                                    // .PaddingRight(70)
                                    .Width(2 * 20)
                                    .Height(2 * 20)
                                    .Image(new BinaryReader(_model.ConvernorSignature).ReadBytes((int)_model.ConvernorSignature.Length), ImageScaling.Resize);
                                             }
                                           });
       column.Item().Row(row =>
               {
                 row.RelativeItem().AlignLeft().Text(text =>
                               {
                                 text.Line(_model.CheckedBy)
                                   .FontFamily("Cambria")
                                   .FontSize(9).Bold(); ;
                                 text.Line("Scrutinized By")
                                 .FontFamily("Cambria")
                                 .FontSize(9).Bold(); ;
                               });
                 row.RelativeItem()
                               .AlignCenter().Text(text =>
                               {
                                 text.Line(_model.Chairman)
                                   .FontFamily("Cambria")
                                   .FontSize(9).Bold(); ;
                                 text.Line("Convener, Membership Development Committee")
                                 .FontFamily("Cambria")
                                 .FontSize(9).Bold(); ;
                               });

               });




       column.Item().Row(row =>
                                   {
                                     if (_model.IsDirectorGeneral || _model.DgDesignateSignature != null)
                                     {
                                       row.RelativeItem(1)
                           .Width(2 * 20)
                           .Height(2 * 20)
                           .Image(new BinaryReader(_model.DgDesignateSignature).ReadBytes((int)_model.DgDesignateSignature.Length), ImageScaling.Resize);

                                       row.RelativeItem(1).AlignCenter()
                            // .PaddingRight(70)
                            .Width(2 * 20)
                            .Height(2 * 20)
                            .Image(new BinaryReader(_model.DgSignature).ReadBytes((int)_model.DgSignature.Length), ImageScaling.Resize);
                                     }
                                   });


       column.Item().Row(row =>
                    {
                      row.RelativeItem().Text(text =>
                                    {
                                      text.Line(_model.Director)
                                   .FontFamily("Cambria")
                                   .FontSize(9).Bold();
                                      text.Line("General Manager")
                                 .FontFamily("Cambria")
                                 .FontSize(9).Bold(); ;
                                    });
                      row.RelativeItem()
                                    .AlignCenter().Text(text =>
                                    {
                                      text.Line(_model.DirectorGeneral)
                                   .FontFamily("Cambria")
                                   .FontSize(9).Bold(); ;
                                      text.Line("Director General")
                                 .FontFamily("Cambria")
                                 .FontSize(9).Bold();
                                    });

                    });



       column.Item().Row(row => row.RelativeItem(2f).BorderTop(1f));

       column.Item().PaddingLeft(20f).PaddingRight(20f).AlignLeft().Text(text =>

                     {
                       text.Line("Approved in the MCM dated :  " + _model.ApprovedMcmDate)
                             .FontFamily("Cambria")
                             .FontSize(10).Bold();
                       text.Span("Membership No. allotted :")
                         .FontFamily("Cambria")
                         .FontSize(10).Bold();
                       text.Span(_model.MembershipId)
                           .FontFamily("Cambria").FontColor("#ff0000")
                           .FontSize(10).Bold();
                     });









       //   column.Item().Row(row =>
       //                           {
       //                              row.RelativeItem()
       //                             .AlignRight().Text(text =>
       //                             {
       //                               text.Line(_model.Secretary)
       //                               .FontFamily("Cambria")
       //                               .FontSize(9).Bold();
       //                             text.Line("Hon. Secretary")
       //                               .FontFamily("Cambria")
       //                               .FontSize(9).Bold();
       //                           });
       column.Item().Row(row =>
                                          {

                                            if (_model.SecratorySignature != null)
                                            {
                                              row.RelativeItem().AlignRight()
                                  .Width(4 * 20)
                                  .Height(3 * 20)
                                  .Image(new BinaryReader(_model.SecratorySignature).ReadBytes((int)_model.SecratorySignature.Length), ImageScaling.Resize);
                                            }
                                            else
                                            {
                                              row.RelativeItem()
                                                                    .AlignRight().Text(text =>
                                                                    {
                                                                      text.Line(_model.Secretary)
                                                                .FontFamily("Cambria")
                                                                .FontSize(9).Bold();
                                                                      text.Line("Hon. Secretary")
                                                             .FontFamily("Cambria")
                                                             .FontSize(9).Bold();
                                                                    });
                                            }
                                          });
       static IContainer DefaultCellStyle(IContainer container, string backgroundColor)
       {
         return container.Border(1f).PaddingHorizontal(1f).AlignLeft();
       }



       static IContainer DefaultCellStyleForSno(IContainer container, string backgroundColor)
       {
         return container.Border(1f).PaddingHorizontal(1f).AlignCenter();
       }

       static IContainer CellStyle2(IContainer container)
       {
         return DefaultCellStyleForSno(container, "#ffffff").ShowOnce();
       }
     });
    }
    void ComposeOrdinaryContent(IContainer container)
    {
      DateTime date = TimeZoneInfo.ConvertTimeFromUtc(_model.CreatedDate, TimeZoneInfo.FindSystemTimeZoneById("Asia/Kolkata")).Date;
      DateTime dateOfPayment = TimeZoneInfo.ConvertTimeFromUtc(_model.DateOfPayment, TimeZoneInfo.FindSystemTimeZoneById("Asia/Kolkata")).Date;
      container.Column(column =>
     {
       column.Item()
           .AlignLeft()
           .PaddingLeft(20)
           .PaddingRight(20)
              .Text(text =>
              {

                text.Span("Date: " + _model.Date.ToString("dd-MM-yyyy")).FontSize(10)
                          .FontFamily("Cambria");

              });
       column.Item()

          .PaddingLeft(20)
          .PaddingRight(20)
            .AlignLeft()
          .Text(text =>
          {

            text.Span("Office Note:").FontSize(10)
                      .FontFamily("Cambria");


          });
       column.Item()

          .PaddingLeft(20)
          .PaddingRight(20)
           .AlignLeft()
          .Text(text =>
          {

            text.Span($"{_model.DirectorGeneral}").FontSize(10)
                      .FontFamily("Cambria");

          });
       column.Item()

            .PaddingLeft(20)
            .PaddingRight(20)
            .AlignLeft()
            .Text(text =>
            {

              text.Span($"Director General{Environment.NewLine}").FontSize(10)
                        .FontFamily("Cambria");

            });

       column.Item()
            .PaddingLeft(20)
            .AlignLeft()
             .Text(text =>
             {

               text.Span("Sub: CREDAI-Pune ").FontSize(10)
                          .FontFamily("Cambria");
               text.Span("Ordinary Membership Application ").FontSize(10)
                       .FontSize(10).Bold()
                       .FontFamily("Cambria");
               text.Span($"for approval.{Environment.NewLine}").FontSize(10)
                      .FontFamily("Cambria");
             });

       column.Item()
             .PaddingLeft(20)
             .PaddingRight(20)
             .AlignLeft()
             .Text(text =>
             {

               text.Span(@$"With reference to above, we are sending as enclosed herewith following Ordinary Membership application for the period from April 20{GetFinancialYear().Result.Split('-')[0]} to
March 20{GetFinancialYear().Result.Split('-')[1]} along with the documents submitted by them for your kind consideration. The detail of the ").FontSize(10)
                     .FontFamily("Cambria");

             });
       column.Item()
            .MinimalBox()
            .PaddingHorizontal(2)
            .Border(1)
            .Table(table =>
  {
    IContainer DefaultCellStyle(IContainer container, string backgroundColor)
    {
      return container
                                            .Border(1)
                                         .PaddingVertical(10)
                                          .PaddingHorizontal(10);
    }

    table.ColumnsDefinition(columns =>
        {
          columns.ConstantColumn(190f);
          columns.ConstantColumn(190f);
          columns.ConstantColumn(150f);
        });
    string[] source = _model.ProprietorOrPartnerNameOrDirectors.Split(',');
    string str = "";
    int count7 = 3;
    if (source.Length <= count7)
    {
      str = string.Join("\n", source);
    }
    else
    {
      for (int count8 = 0; count8 < source.Length; count8 += count7)
      {
        string[] array = ((IEnumerable<string>)source).Skip<string>(count8).Take<string>(count7).ToArray<string>();
        str = str + string.Join(", ", array) + "\n";
      }
    }
    table.Header(header =>
        {
          // please be sure to call the 'header' handler!

          header.Cell().ColumnSpan(1).Element(CellStyle).ExtendHorizontal().AlignLeft().Text("Name of Applicant Organization").FontSize(10).FontFamily("Cambria").Bold();
          header.Cell().ColumnSpan(1).Element(CellStyle).Text("Name of the Proprietor/Partner/Directors").FontSize(10).FontFamily("Cambria").Bold();
          header.Cell().ColumnSpan(1).Element(CellStyle).Text("Name of	the Proposer/Seconder").FontSize(10).FontFamily("Cambria").Bold();

          // you can extend existing styles by creating additional methods
          IContainer CellStyle(IContainer container) => DefaultCellStyle(container, Colors.Grey.Lighten3);
        });
    table.Cell().Element(CellStyle).Text(text =>

       {
         text.Line($"{_model.Firm}").FontSize(10).FontFamily("Cambria");
         text.Line($"").FontSize(10).FontFamily("Cambria");
         text.Span("Address:").FontSize(10).FontFamily("Cambria").Bold();
         text.Span($"{_model.Address}").FontSize(10).FontFamily("Cambria");
       });
    // ));
    table.Cell().Element(CellStyle).Text(str).FontSize(10).FontFamily("Cambria");
    // table.Cell().Element(CellStyle).Text($"M/s.{_model.OrdinaryMemberName}").FontSize(10).FontFamily("Cambria");

    table.Cell().Element(CellStyle).Text(text =>
          {
            text.Span("ProposarName: ").FontSize(9).FontFamily("Cambria").Bold();
            text.Line(_model.ProposerName).FontSize(9).FontFamily("Cambria");
            text.Span("ProposarFirmName: ").FontSize(9).FontFamily("Cambria").Bold();
            text.Line(_model.ProposerFirmName).FontSize(9).FontFamily("Cambria");
            text.Span("SeconderName: ").FontSize(9).FontFamily("Cambria").Bold();
            text.Line(_model.SecondaryName).FontSize(9).FontFamily("Cambria");
            text.Span("SeconderFirmName: ").FontSize(9).FontFamily("Cambria").Bold();
            text.Line(_model.SecondaryFirmName).FontSize(9).FontFamily("Cambria");



          });

















    IContainer CellStyle(IContainer container) => DefaultCellStyle(container, Colors.Grey.Lighten3);



    // IContainer CellStyle(IContainer container) => DefaultCellStyle(container, Colors.White).ShowOnce();
  });
       column.Item()
             .PaddingLeft(20)
             .PaddingRight(20)
            .AlignLeft()
             .Text(text =>
             {

               text.Line($"").FontSize(10)

                      .FontFamily("Cambria");
               text.Span($"It has been confirmed from the records of the CREDAI-Pune that as on today the above Proposer and Seconder do not have any outstanding dues and have paid Annual Subscription Charges for F.Y. 20{GetFinancialYear().Result.Split('-')[0]}-20{GetFinancialYear().Result.Split('-')[1]}.").FontSize(10)

                          .FontFamily("Cambria");
               text.Line($"").FontSize(10)

                    .FontFamily("Cambria");

             });
       column.Item()
            .PaddingLeft(20)
            .PaddingRight(20)
           .AlignLeft()
            .Text(text =>
            {
              text.Span($"The firm has submitted following documents along with the application:").FontSize(10)
                         .FontFamily("Cambria");
            });
       column.Item()

           .MinimalBox()
           .Border(1)
           .Table(table =>
{
  IContainer DefaultCellStyle(IContainer container, string backgroundColor)
  {
    return container
             .Border(1)
             //  .BorderColor(Colors.Grey.Lighten1)
             //  .Background(backgroundColor)
             .PaddingHorizontal(1)
             .AlignLeft();




  }
  IContainer DefaultCellStyleForSno(IContainer container, string backgroundColor)
  {
    return container
             .Border(1)
             //  .BorderColor(Colors.Grey.Lighten1)
             //  .Background(backgroundColor)
             .PaddingHorizontal(1)
             .AlignCenter();




  }

  table.ColumnsDefinition(columns =>
     {
       columns.ConstantColumn(30f);
       columns.ConstantColumn(150f);
       columns.ConstantColumn(165f);
       columns.ConstantColumn(175f);



     });




  table.Header(header =>
     {
       // please be sure to call the 'header' handler!

       header.Cell().RowSpan(2).Element(CellStyle).ExtendHorizontal().AlignLeft().Text("");
       header.Cell().ColumnSpan(1).AlignCenter().Text("Document").FontSize(12).FontFamily("Cambria").FontSize(11).Bold();
       header.Cell().ColumnSpan(2).Element(CellStyle).AlignCenter().PaddingLeft(100f).Text("Remark").FontSize(12).FontFamily("Cambria").Bold();

       // you can extend existing styles by creating additional methods
       IContainer CellStyle(IContainer container) => DefaultCellStyle(container, Colors.Grey.Lighten3);
     });

  table.Cell().Row(1).Column(1).Element(CellStyle2).AlignLeft().Text("1.").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(2).Column(1).Element(CellStyle2).AlignLeft().Text("2.").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(7).Column(1).Element(CellStyle2).AlignLeft().Text("7.").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(10).Column(1).Element(CellStyle2).AlignLeft().Text("8.").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(1).Column(2).Element(CellStyle).ExtendHorizontal().AlignLeft().Text("PAN Card No. of firm").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(2).Column(2).Element(CellStyle).ExtendHorizontal().AlignLeft().Text("GST No. of firm").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(3).Column(2).Element(CellStyle).ExtendHorizontal().AlignLeft().Text("Copy of Aadhaar/PAN Card of the Proprietor/Partners/Directors/Members").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(4).Column(2).Element(CellStyle).ExtendHorizontal().AlignLeft().Text("Partnership Deed/MOA/Articles of Association/Shop Act Licence/AOP Agreement/LLP Agreements").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(5).Column(2).Element(CellStyle).ExtendHorizontal().AlignLeft().Text("Commencement/NA order").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(6).Column(2).Element(CellStyle).ExtendHorizontal().AlignLeft().Text("Copy of Code of Conduct ").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(7).Column(2).AlignLeft().Text("Payment Details").Bold().FontSize(10).FontFamily("Cambria");
  table.Cell().Row(8).Column(2).Element(CellStyle).ExtendHorizontal().AlignLeft().Text($"Amount:- Rs.{_model.Amt}/-").Bold().FontSize(10).FontFamily("Cambria");
  table.Cell().Row(9).Column(2).Element(CellStyle).ExtendHorizontal().AlignLeft().Text("R.No.").Bold().FontSize(10).FontFamily("Cambria");
  table.Cell().Row(10).Column(2).Element(CellStyle).ExtendHorizontal().AlignLeft().Text("ApplicationId & Date:").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(1).Column(3).Element(CellStyle).ExtendHorizontal().PaddingLeft(5).Text($"{_model.PanCardNumber}").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(2).Column(3).Element(CellStyle).ExtendHorizontal().PaddingLeft(5).Text($"{_model.GstNumber}").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(3).Column(3).Element(CellStyle).ExtendHorizontal().AlignCenter().Text($"Submitted").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(1).Column(4).Element(CellStyle).ExtendHorizontal().AlignLeft().Text("Verified").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(2).Column(4).Element(CellStyle).ExtendHorizontal().AlignLeft().Text("Verified").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(3).ColumnSpan(4).Element(CellStyle).ExtendHorizontal().AlignRight().Text("").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(4).ColumnSpan(4).Element(CellStyle).ExtendHorizontal().AlignRight().Text("").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(5).ColumnSpan(4).Element(CellStyle).ExtendHorizontal().AlignLeft().Text("").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(6).ColumnSpan(4).Element(CellStyle).ExtendHorizontal().AlignLeft().Text("").FontSize(10).FontFamily("Cambria");
  //  table.Cell().Row(3).ColumnSpan(4).Element(CellStyle).ExtendHorizontal().AlignCenter().PaddingRight(30f).Text("Submitted").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(4).Column(3).Element(CellStyle).ExtendHorizontal().AlignCenter().Text(_model.FileExists ? "Submitted" : "NotSubmitted").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(5).Column(3).Element(CellStyle).ExtendHorizontal().AlignCenter().Text(_model.CommencemenceFileExists ? "Submitted" : "NotSubmitted").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(6).Column(3).Element(CellStyle).ExtendHorizontal().AlignCenter().Text("Submitted").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(8).Column(3).Element(CellStyle).AlignLeft().Text("TransactionId:- " + _model.TransactionId).FontSize(10).FontFamily("Cambria");
  table.Cell().Row(8).Column(4).Element(CellStyle).AlignLeft().Text("Date :" + _model.DateOfPayment.ToString("dd-MM-yyyy")).FontSize(10).FontFamily("Cambria");
  table.Cell().RowSpan(2).ColumnSpan(2).Element(CellStyle).ExtendHorizontal().AlignLeft().Text(_model.ReceiptNumber).Bold().FontSize(10).FontFamily("Cambria");

  table.Cell().Row(3).Column(1).Element(CellStyle2).Text("3.").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(4).Column(1).Element(CellStyle2).Text("4.").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(5).Column(1).Element(CellStyle2).Text("5.").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(6).Column(1).Element(CellStyle2).Text("6.").FontSize(10).FontFamily("Cambria");

  table.Cell().Row(10).Column(3).Element(CellStyle).ExtendHorizontal().AlignLeft().Text(_model.ApplicationId).FontSize(10).FontFamily("Cambria");
  table.Cell().Row(10).Column(4).Element(CellStyle).ExtendHorizontal().AlignRight().Text("").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(10).Column(4).Element(CellStyle).ExtendHorizontal().AlignLeft().Text(date.ToString("dd-MM-yyyy")).FontSize(10).FontFamily("Cambria");

  IContainer CellStyle(IContainer container)
  {
    return DefaultCellStyle(container, "#ffffff").ShowOnce();
  }
});
















       column.Item()
           .AlignLeft()
            .Text(text =>
            {

              text.Span($" Submitting the application for your Consideration & Recommendation").FontSize(10)
                         .FontSize(10).Bold()
                         .FontFamily("Cambria");
              text.Line($"").FontSize(10)
                         .FontSize(10).Bold()
                         .FontFamily("Cambria");

            });
       column.Item().Row(row =>
                                           {
                                             if (_model.IsScrutinizer || _model.VerifiedBySignature != null)
                                             {
                                               row.RelativeItem(1)
                                   .Width(2 * 20)
                                   .Height(2 * 20)
                                   .Image(new BinaryReader(_model.VerifiedBySignature).ReadBytes((int)_model.VerifiedBySignature.Length), ImageScaling.Resize);
                                             }
                                             if (_model.IsConvernor || _model.ConvernorSignature != null)
                                             {
                                               row.RelativeItem(1).AlignCenter()
                                    // .PaddingRight(70)
                                    .Width(2 * 20)
                                    .Height(2 * 20)
                                    .Image(new BinaryReader(_model.ConvernorSignature).ReadBytes((int)_model.ConvernorSignature.Length), ImageScaling.Resize);
                                             }
                                           });
       column.Item().Row(row =>
               {
                 row.RelativeItem().AlignLeft().Text(text =>
                               {
                                 text.Line(_model.CheckedBy)
                                   .FontFamily("Cambria")
                                   .FontSize(9).Bold(); ;
                                 text.Line("Scrutinized By")
                                 .FontFamily("Cambria")
                                 .FontSize(9).Bold(); ;
                               });
                 row.RelativeItem()
                               .AlignCenter().Text(text =>
                               {
                                 text.Line(_model.Chairman)
                                   .FontFamily("Cambria")
                                   .FontSize(9).Bold(); ;
                                 text.Line("Convener, Membership Development Committee")
                                 .FontFamily("Cambria")
                                 .FontSize(9).Bold(); ;
                               });

               });




       column.Item().Row(row =>
                                   {
                                     if (_model.IsDirectorGeneral || _model.DgDesignateSignature != null)
                                     {
                                       row.RelativeItem(1)
                           .Width(2 * 20)
                           .Height(2 * 20)
                           .Image(new BinaryReader(_model.DgDesignateSignature).ReadBytes((int)_model.DgDesignateSignature.Length), ImageScaling.Resize);

                                       row.RelativeItem(1).AlignCenter()
                            // .PaddingRight(70)
                            .Width(2 * 20)
                            .Height(2 * 20)
                            .Image(new BinaryReader(_model.DgSignature).ReadBytes((int)_model.DgSignature.Length), ImageScaling.Resize);
                                     }
                                   });


       column.Item().Row(row =>
                    {
                      row.RelativeItem().Text(text =>
                                    {
                                      text.Line(_model.Director)
                                   .FontFamily("Cambria")
                                   .FontSize(9).Bold();
                                      text.Line("General Manager")
                                 .FontFamily("Cambria")
                                 .FontSize(9).Bold(); ;
                                    });
                      row.RelativeItem()
                                    .AlignCenter().Text(text =>
                                    {
                                      text.Line(_model.DirectorGeneral)
                                   .FontFamily("Cambria")
                                   .FontSize(9).Bold(); ;
                                      text.Line("Director General")
                                 .FontFamily("Cambria")
                                 .FontSize(9).Bold();
                                    });

                    });



       column.Item().Row(row => row.RelativeItem(2f).BorderTop(1f));

       column.Item().PaddingLeft(20f).PaddingRight(20f).AlignLeft().Text(text =>

                     {
                       text.Line("Approved in the MCM dated :  " + _model.ApprovedMcmDate)
                             .FontFamily("Cambria")
                             .FontSize(10).Bold();
                       text.Span("Membership No. allotted :")
                         .FontFamily("Cambria")
                         .FontSize(10).Bold();
                       text.Span(_model.MembershipId)
                           .FontFamily("Cambria").FontColor("#ff0000")
                           .FontSize(10).Bold();
                     });









       //   column.Item().Row(row =>
       //                           {
       //                              row.RelativeItem()
       //                             .AlignRight().Text(text =>
       //                             {
       //                               text.Line(_model.Secretary)
       //                               .FontFamily("Cambria")
       //                               .FontSize(9).Bold();
       //                             text.Line("Hon. Secretary")
       //                               .FontFamily("Cambria")
       //                               .FontSize(9).Bold();
       //                           });
       column.Item().Row(row =>
                                          {

                                            if (_model.SecratorySignature != null)
                                            {
                                              row.RelativeItem().AlignRight()
                                  .Width(4 * 35)
                                  .Height(3 * 20)
                                  .Image(new BinaryReader(_model.SecratorySignature).ReadBytes((int)_model.SecratorySignature.Length), ImageScaling.Resize);
                                            }
                                            else
                                            {
                                              row.RelativeItem()
                                                                    .AlignRight().Text(text =>
                                                                    {
                                                                      text.Line(_model.Secretary)
                                                                .FontFamily("Cambria")
                                                                .FontSize(9).Bold();
                                                                      text.Line("Hon. Secretary")
                                                             .FontFamily("Cambria")
                                                             .FontSize(9).Bold();
                                                                    });
                                            }
                                          });
       static IContainer DefaultCellStyle(IContainer container, string backgroundColor)
       {
         return container.Border(1f).PaddingHorizontal(1f).AlignLeft();
       }



       static IContainer DefaultCellStyleForSno(IContainer container, string backgroundColor)
       {
         return container.Border(1f).PaddingHorizontal(1f).AlignCenter();
       }

       static IContainer CellStyle2(IContainer container)
       {
         return DefaultCellStyleForSno(container, "#ffffff").ShowOnce();
       }
     });
    }

    // void ComposeOrdinaryHeader(IContainer container)
    // {
    //     container.Column(column =>
    //     {

    //     });
    // }
    //         void ComposeOrdinaryContent(IContainer container)
    //         {
    //             container.Column(column =>
    //             {
    //                 column.Item()
    //                  .AlignLeft()
    //                  .PaddingLeft(20)
    //                  .PaddingRight(20)
    //                     .Text(text =>
    //                     {

    //                         text.Span($"Date: {_model.Date}").FontSize(10)
    //                             .FontFamily("Cambria");

    //                     });
    //                 column.Item()

    //              .PaddingLeft(20)
    //              .PaddingRight(20)
    //                .AlignLeft()
    //              .Text(text =>
    //              {

    //                  text.Span("Office Note:").FontSize(10)
    //                      .FontFamily("Cambria");


    //              });
    //                 column.Item()

    //              .PaddingLeft(20)
    //              .PaddingRight(20)
    //               .AlignLeft()
    //              .Text(text =>
    //              {

    //                  text.Span("Dr.D.K. Abhyankar").FontSize(10)
    //                      .FontFamily("Cambria");

    //              });
    //                 column.Item()

    //                .PaddingLeft(20)
    //                .PaddingRight(20)
    //                .AlignLeft()
    //                .Text(text =>
    //                {

    //                    text.Span($"Director General").FontSize(10)
    //                        .FontFamily("Cambria");

    //                });

    //                 column.Item()
    //                .PaddingLeft(20).PaddingTop(20)
    //                .AlignLeft()
    //                 .Text(text =>
    //                 {

    //                     text.Span("Sub: CREDAI-Pune Metro ").FontSize(10)
    //                          .FontFamily("Cambria");
    //                     text.Span("Ordinary Membership Application ").FontSize(10)
    //                       .FontSize(10).Bold()
    //                       .FontFamily("Cambria");
    //                     text.Span($"for approval.").FontSize(10)
    //                      .FontFamily("Cambria");
    //                 });

    //                 column.Item()
    //                 .PaddingLeft(20)
    //                 .PaddingTop(20)
    //                 .PaddingRight(20)
    //                 .AlignLeft()
    //                 .Text(text =>
    //                 {

    //                     text.Span("With reference to above, we are sending as enclosed herewith following Ordinary Membership application for the period from April 2023 to March 2024 along with the documents submitted by them for your kind consideration. The detail of the firm applied is as below").FontSize(10)
    //                     .FontFamily("Cambria");

    //                 });
    //                 column.Item()
    //               .MinimalBox()
    //               .Border(1)
    //               .Table(table =>
    //    {
    //        IContainer DefaultCellStyle(IContainer container, string backgroundColor)
    //        {
    //            return container
    //                                             .Border(1)
    //                                          .PaddingVertical(10)
    //                                           .PaddingHorizontal(10);
    //        }

    //        table.ColumnsDefinition(columns =>
    //        {
    //            columns.RelativeColumn();
    //            columns.RelativeColumn();
    //            columns.RelativeColumn();
    //            //   columns.ConstantColumn(75);
    //            //   columns.ConstantColumn(75);
    //        });

    //        table.Header(header =>
    //      {
    //          // please be sure to call the 'header' handler!

    //          header.Cell().ColumnSpan(1).Element(CellStyle).ExtendHorizontal().AlignLeft().Text("Name of Applicant Organization").FontSize(10).FontFamily("Cambria").Bold();
    //          header.Cell().ColumnSpan(1).Element(CellStyle).Text("Name of the Partners/Proprietor/Directors").FontSize(10).FontFamily("Cambria").Bold();
    //          header.Cell().ColumnSpan(1).Element(CellStyle).Text("Name of the Proposer/Seconder").FontSize(10).FontFamily("Cambria").Bold();

    //          // you can extend existing styles by creating additional methods
    //          IContainer CellStyle(IContainer container) => DefaultCellStyle(container, Colors.Grey.Lighten3);
    //      });
    //        table.Cell().Element(CellStyle).Text(text =>

    //     {
    //         text.Line($"{_model.Firm}").FontSize(10).FontFamily("Cambria");
    //         text.Span("Address:").FontSize(10).FontFamily("Cambria").Bold();
    //         text.Span($"{_model.Address}").FontSize(10).FontFamily("Cambria");
    //     });
    //        // ));
    //        table.Cell().Element(CellStyle).Text(_model.ProprietorOrPartnerNameOrDirectors).FontSize(10).FontFamily("Cambria");
    //        table.Cell().Element(CellStyle).Text(
    //         text =>
    //         {
    //             text.Line($"ProposarName: {_model.ProposerName}").FontSize(10).FontFamily("Cambria");
    //             text.Line($"SecondaryName: {_model.SecondaryName}").FontSize(10).FontFamily("Cambria");
    //         });
    //        IContainer CellStyle(IContainer container) => DefaultCellStyle(container, Colors.Grey.Lighten3);
    //        //   table.Cell().Row(1).Column(1).Element(CellStyle).ExtendHorizontal().AlignLeft().Text($"M/s. {_model.Name}").FontSize(10).FontFamily("Cambria");
    //        //   table.Cell().Row(2).Column(1).Element(CellStyle).ExtendHorizontal().AlignLeft().Text($"Address: {_model.Address}").FontSize(10).FontFamily("Cambria");
    //        //   table.Cell().ColumnSpan(2).Element(CellStyle).Text($"sxaxaxa").FontSize(10).FontFamily("Cambria");
    //        //   table.Cell().ColumnSpan(2).Element(CellStyle).Text($"M/s. {_model.Name}").FontSize(10).FontFamily("Cambria");



    //        // IContainer CellStyle(IContainer container) => DefaultCellStyle(container, Colors.White).ShowOnce();
    //    });
    //                 column.Item()
    //                 .PaddingLeft(20)
    //                 .PaddingRight(20)
    //                .AlignLeft()
    //                 .Text(text =>
    //                 {

    //                     text.Span($"{Environment.NewLine}It has been confirmed from the records of the CREDAI-Pune Metro that as on today the above Proposer and Seconder do not have any outstanding dues and have paid Annual Subscription Charges for F.Y. 2022-2023. The proposer and seconder are paid the Subscription for F.Y.23-24 {Environment.NewLine}").FontSize(10)
    //                          .FontSize(10)
    //                          .FontFamily("Cambria");

    //                 });
    //                 column.Item()
    //                .PaddingLeft(20)
    //                .PaddingRight(20)
    //               .AlignLeft()
    //                .Text(text =>
    //                {
    //                    text.Span($"The firm has submitted following documents along with the application:{Environment.NewLine}").FontSize(10)
    //                  .FontFamily("Cambria");
    //                    text.Span($"1) Request letter to adjust the subscription paid for {_model.BuilderName}{Environment.NewLine}").FontSize(10)
    //                   .FontFamily("Cambria");
    //                    text.Span($"2) Photo copy of PAN Card and GST Certificate.{Environment.NewLine}").FontSize(10)
    //                  .FontFamily("Cambria");
    //                    text.Span($"3) Photo copy of Shop Act license.{Environment.NewLine}").FontSize(10)
    //                  .FontFamily("Cambria");
    //                    text.Span($"4) Photo copy of Registration certification no.{Environment.NewLine}").FontSize(10)
    //                   .FontFamily("Cambria");
    //                    text.Span($"5) Photo copy of Pan card of the Partners.{Environment.NewLine}").FontSize(10)
    //                  .FontFamily("Cambria");
    //                    text.Span($"6) Photo copy of Partnership deed.{Environment.NewLine}").FontSize(10)
    //                   .FontFamily("Cambria");
    //                    text.Span($"7) Photo copy of Completion Certificates of.{Environment.NewLine}").FontSize(10)
    //                  .FontFamily("Cambria");
    //                    text.Span($"8) Photo copy of Commencement certificate of.{Environment.NewLine}").FontSize(10)
    //                  .FontFamily("Cambria");
    //                    text.Span($"10) Payment done by Chq.no {_model.ChqNo} dated {_model.DateOfPayment} of amounting Rs.{_model.Amt}{Environment.NewLine}").FontSize(10)
    //                  .FontFamily("Cambria");

    //                });

    //                 column.Item()
    //                .PaddingLeft(20)
    //                .PaddingRight(20)
    //               .AlignLeft()
    //                .Text(text =>
    //                {

    //                    text.Span($"Submitting the application for your Consideration & Recommendation{Environment.NewLine}").FontSize(10)
    //                         .FontSize(10).Bold()
    //                         .FontFamily("Cambria");

    //                });

    //                 column.Item().PaddingLeft(20).PaddingRight(20).Row(row =>
    //                   {
    //                       row.RelativeItem(1)
    //                               .AlignLeft().Text(text =>
    //                               {
    //                                   text.Span($"Ms.Shruti Shende{Environment.NewLine}")
    //                                   .FontFamily("Cambria")
    //                                   .FontSize(10);
    //                                   text.Span($"Prepared By{Environment.NewLine}{Environment.NewLine}")
    //                                 .FontFamily("Cambria")
    //                                 .FontSize(10);
    //                               });
    //                       row.RelativeItem(1)
    //                               .AlignRight().Text(text =>
    //                               {
    //                                   text.Span($"Mr. Mayur Ekbote{Environment.NewLine}")
    //                                   .FontFamily("Cambria")
    //                                   .FontSize(10);
    //                                   text.Span($"Account Checked By{Environment.NewLine}{Environment.NewLine}")
    //                                 .FontFamily("Cambria")
    //                                 .FontSize(10);
    //                               });

    //                   });
    //                 column.Item().PaddingLeft(20).PaddingRight(20).Row(row =>
    //                {
    //                    row.RelativeItem(1)
    //                            .AlignLeft().Text(text =>
    //                            {
    //                                text.Span($"Ms.U.K. Julka{Environment.NewLine}")
    //                                .FontFamily("Cambria")
    //                                .FontSize(10);
    //                                text.Span($"Checked By{Environment.NewLine}{Environment.NewLine}")
    //                              .FontFamily("Cambria")
    //                              .FontSize(10);
    //                            });
    //                    row.RelativeItem(1)
    //                            .AlignRight().Text(text =>
    //                            {
    //                                text.Span($"Dr.D.K.Abhyankar{Environment.NewLine}")
    //                                .FontFamily("Cambria")
    //                                .FontSize(10);
    //                                text.Span($"Director General{Environment.NewLine}{Environment.NewLine}")
    //                              .FontFamily("Cambria")
    //                              .FontSize(10);
    //                            });

    //                });

    //                 column.Item()
    //                .AlignCenter()
    //               .Text(text =>
    //            {

    //                text.Span("J.P. Shroff").FontSize(10)
    //               .FontSize(10)
    //               .FontFamily("Cambria");


    //            });
    //                 column.Item()

    //                   .AlignCenter()
    //                   .Text(text =>
    //                {


    //                    text.Span($"Convener, Membership Development Committee{Environment.NewLine}").FontSize(10)
    //                  .FontSize(10)
    //                  .FontFamily("Cambria");


    //                });
    //                 column.Item().Row(row =>
    //                {
    //                    row.RelativeItem(2)
    //                                   .BorderTop(1);
    //                });
    //                 column.Item()
    //                .PaddingLeft(20)
    //                .PaddingRight(20)
    //                .PaddingVertical(10)
    //               .AlignLeft()
    //                .Text(text =>
    //                {

    //                    text.Span($"Approved in the MCM dated :  {_model.ApprovedMcmDate}                                   {Environment.NewLine}{Environment.NewLine}").FontSize(10)
    //                         .FontSize(10).Bold()
    //                         .FontFamily("Cambria");

    //                });
    //                 column.Item()
    //                .PaddingLeft(20)
    //                .PaddingRight(20)
    //               .AlignLeft()
    //                .Text(text =>
    //                {

    //                    text.Span($"Membership No. allotted :{_model.ApplicantId}").FontSize(10)
    //                         .FontSize(10).Bold()
    //                         .FontFamily("Cambria");

    //                });
    //                 column.Item().PaddingTop(30)
    //                                 .Row(row =>
    //                                 {
    //                                     row.RelativeItem(1)
    //                                             .AlignCenter()
    //                .Text(text =>
    //                {

    //                    text.Span($"Ashwin Trimal").FontSize(10)
    //                         .FontSize(10).Bold()
    //                         .FontFamily("Cambria");

    //                });
    //                                     row.RelativeItem(1)
    //                                                                 .AlignCenter()
    //                                .Text(text =>
    //                                {

    //                                    text.Span($"Ranjit Naiknavare").FontSize(10)
    //                                         .FontSize(10).Bold()
    //                                         .FontFamily("Cambria");





    //                                });
    //                                 });

    //                 column.Item()
    //                                              .Row(row =>
    //                                              {
    //                                                  row.RelativeItem(1)
    //                                                          .AlignCenter()
    //                             .Text(text =>
    //                             {

    //                                 text.Span($"Hon. Secretary").FontSize(10)
    //                                      .FontSize(10).Bold()
    //                                      .FontFamily("Cambria");

    //                             });
    //                                                  row.RelativeItem(1)
    //                                                                              .AlignCenter()
    //                                             .Text(text =>
    //                                             {

    //                                                 text.Span($"President").FontSize(10)
    //                                                      .FontSize(10).Bold()
    //                                                      .FontFamily("Cambria");





    //                                             });
    //                                              });


    //             });
    //         }
    // void ComposeAssociateHeader(IContainer container)
    // {
    //     container.Column(column =>
    //     {
    //         // var stream = Assembly.GetExecutingAssembly().GetManifestResourceStream("recommendedPlugin.Logo.png");
    //         // if (stream != null)
    //         // {

    //         //     column.Item().Row(row =>
    //         //     {
    //         //         row.RelativeItem(4);
    //         //         row.RelativeItem(1)
    //         //             .AlignCenter()
    //         //             .Height(50)
    //         //             .Image(new BinaryReader(stream).ReadBytes((int)stream.Length), ImageScaling.Resize);
    //         //         row.RelativeItem(4);
    //         //     });
    //         // }

    //     });
    // }
    void ComposeAssociateContent(IContainer container)
    {
      DateTime date = TimeZoneInfo.ConvertTimeFromUtc(_model.CreatedDate, TimeZoneInfo.FindSystemTimeZoneById("Asia/Kolkata")).Date;
      DateTime dateOfPayment = TimeZoneInfo.ConvertTimeFromUtc(_model.DateOfPayment, TimeZoneInfo.FindSystemTimeZoneById("Asia/Kolkata")).Date;
      container.Column(column =>
     {
       column.Item()
           .AlignLeft()
           .PaddingLeft(20)
           .PaddingRight(20)
              .Text(text =>
              {

                text.Span("Date: " + _model.Date.ToString("dd-MM-yyyy")).FontSize(10)
                          .FontFamily("Cambria");

              });
       column.Item()

          .PaddingLeft(20)
          .PaddingRight(20)
            .AlignLeft()
          .Text(text =>
          {

            text.Span("Office Note:").FontSize(10)
                      .FontFamily("Cambria");


          });
       column.Item()

          .PaddingLeft(20)
          .PaddingRight(20)
           .AlignLeft()
          .Text(text =>
          {

            text.Span($"{_model.DirectorGeneral}").FontSize(10)
                      .FontFamily("Cambria");

          });
       column.Item()

            .PaddingLeft(20)
            .PaddingRight(20)
            .AlignLeft()
            .Text(text =>
            {

              text.Span($"Director General{Environment.NewLine}").FontSize(10)
                        .FontFamily("Cambria");

            });

       column.Item()
            .PaddingLeft(20)
            .AlignLeft()
             .Text(text =>
             {

               text.Span("Sub: CREDAI-Pune ").FontSize(10)
                          .FontFamily("Cambria");
               text.Span("Associate Membership Application ").FontSize(10)
                       .FontSize(10).Bold()
                       .FontFamily("Cambria");
               text.Span($"for approval.{Environment.NewLine}").FontSize(10)
                      .FontFamily("Cambria");
             });

       column.Item()
             .PaddingLeft(20)
             .PaddingRight(20)
             .AlignLeft()
             .Text(text =>
             {

               text.Span("With reference to above, we are submitting herewith following application for Associate Membership along with the documents submitted by them for your kind consideration. The details of the firm applied are as below.").FontSize(10)
                     .FontFamily("Cambria");

             });
       column.Item()
            .MinimalBox()
            .Border(1)
            .Table(table =>
  {
    IContainer DefaultCellStyle(IContainer container, string backgroundColor)
    {
      return container
                                            .Border(1)
                                         .PaddingVertical(10)
                                          .PaddingHorizontal(10);
    }

    table.ColumnsDefinition(columns =>
        {
          columns.ConstantColumn(200f);
          columns.ConstantColumn(210f);
          columns.ConstantColumn(110f);
        });
    string[] source = _model.ProprietorOrPartnerNameOrDirectors.Split(',');
    string str = "";
    int count7 = 3;
    if (source.Length <= count7)
    {
      str = string.Join("\n", source);
    }
    else
    {
      for (int count8 = 0; count8 < source.Length; count8 += count7)
      {
        string[] array = ((IEnumerable<string>)source).Skip<string>(count8).Take<string>(count7).ToArray<string>();
        str = str + string.Join(", ", array) + "\n";
      }
    }

    table.Header(header =>
        {
          // please be sure to call the 'header' handler!

          header.Cell().ColumnSpan(1).Element(CellStyle).ExtendHorizontal().AlignLeft().Text("Name of Applicant Organization").FontSize(10).FontFamily("Cambria").Bold();
          header.Cell().ColumnSpan(1).Element(CellStyle).Text("Name of the Proprietor/Partner/Directors").FontSize(10).FontFamily("Cambria").Bold();
          header.Cell().ColumnSpan(1).Element(CellStyle).Text("Name of the Ordinary Member").FontSize(10).FontFamily("Cambria").Bold();

          // you can extend existing styles by creating additional methods
          IContainer CellStyle(IContainer container) => DefaultCellStyle(container, Colors.Grey.Lighten3);
        });
    table.Cell().Element(CellStyle).Text(text =>

       {
         text.Line($"{_model.Firm}").FontSize(10).FontFamily("Cambria");
         text.Line($"").FontSize(10).FontFamily("Cambria");
         text.Span("Address:").FontSize(10).FontFamily("Cambria").Bold();
         text.Span($"{_model.Address}").FontSize(10).FontFamily("Cambria");
       });
    // ));
    table.Cell().Element(CellStyle).Text(str).FontSize(10).FontFamily("Cambria");
    table.Cell().Element(CellStyle).Text($"M/s.{_model.OrdinaryMemberName}").FontSize(10).FontFamily("Cambria");

    IContainer CellStyle(IContainer container) => DefaultCellStyle(container, Colors.Grey.Lighten3);



    // IContainer CellStyle(IContainer container) => DefaultCellStyle(container, Colors.White).ShowOnce();
  });
       column.Item()
             .PaddingLeft(20)
             .PaddingRight(20)
            .AlignLeft()
             .Text(text =>
             {

               text.Span($"{Environment.NewLine}Any dues from Ordinary Member for the F Y {GetFinancialYear().Result.Split('-')[0]}-{GetFinancialYear().Result.Split('-')[1]}: Yes/No {Environment.NewLine}").FontSize(10)
                          .FontSize(10)
                          .FontFamily("Cambria");

             });
       column.Item()
            .PaddingLeft(20)
            .PaddingRight(20)
           .AlignLeft()
            .Text(text =>
            {
              text.Span($"The firm has submitted following documents along with the application:").FontSize(10)
                         .FontFamily("Cambria");
            });
       column.Item()

           .MinimalBox()
           .Border(1)
           .Table(table =>
{
  IContainer DefaultCellStyle(IContainer container, string backgroundColor)
  {
    return container
             .Border(1)
             //  .BorderColor(Colors.Grey.Lighten1)
             //  .Background(backgroundColor)
             .PaddingHorizontal(1)
             .AlignLeft();




  }
  IContainer DefaultCellStyleForSno(IContainer container, string backgroundColor)
  {
    return container
             .Border(1)
             //  .BorderColor(Colors.Grey.Lighten1)
             //  .Background(backgroundColor)
             .PaddingHorizontal(1)
             .AlignCenter();




  }

  table.ColumnsDefinition(columns =>
     {
       columns.ConstantColumn(30f);
       columns.ConstantColumn(150f);
       columns.ConstantColumn(165f);
       columns.ConstantColumn(175f);



     });




  table.Header(header =>
     {
       // please be sure to call the 'header' handler!

       header.Cell().RowSpan(2).Element(CellStyle).ExtendHorizontal().AlignLeft().Text("");
       header.Cell().ColumnSpan(1).AlignCenter().Text("Document").FontSize(12).FontFamily("Cambria").FontSize(11).Bold();
       header.Cell().ColumnSpan(2).Element(CellStyle).AlignCenter().PaddingLeft(100f).Text("Remark").FontSize(12).FontFamily("Cambria").Bold();

       // you can extend existing styles by creating additional methods
       IContainer CellStyle(IContainer container) => DefaultCellStyle(container, Colors.Grey.Lighten3);
     });

  table.Cell().Row(1).Column(1).Element(CellStyle2).AlignLeft().Text("1.").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(2).Column(1).Element(CellStyle2).AlignLeft().Text("2.").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(7).Column(1).Element(CellStyle2).AlignLeft().Text("7.").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(10).Column(1).Element(CellStyle2).AlignLeft().Text("8.").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(1).Column(2).Element(CellStyle).ExtendHorizontal().AlignLeft().Text("PAN Card No. of firm").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(2).Column(2).Element(CellStyle).ExtendHorizontal().AlignLeft().Text("GST No. of firm").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(3).Column(2).Element(CellStyle).ExtendHorizontal().AlignLeft().Text("Copy of Aadhaar/PAN Card of the Proprietor/Partners/Directors/Members").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(4).Column(2).Element(CellStyle).ExtendHorizontal().AlignLeft().Text("Partnership Deed/MOA/Articles of Association/Shop Act Licence/AOP Agreement/LLP Agreements").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(5).Column(2).Element(CellStyle).ExtendHorizontal().AlignLeft().Text("Commencement/NA order").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(6).Column(2).Element(CellStyle).ExtendHorizontal().AlignLeft().Text("Copy of Code of Conduct ").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(7).Column(2).AlignLeft().Text("Payment Details").Bold().FontSize(10).FontFamily("Cambria");
  table.Cell().Row(8).Column(2).Element(CellStyle).ExtendHorizontal().AlignLeft().Text($"Amount:- Rs.{_model.Amt}/-").Bold().FontSize(10).FontFamily("Cambria");
  table.Cell().Row(9).Column(2).Element(CellStyle).ExtendHorizontal().AlignLeft().Text("R.No.").Bold().FontSize(10).FontFamily("Cambria");
  table.Cell().Row(10).Column(2).Element(CellStyle).ExtendHorizontal().AlignLeft().Text("ApplicationId & Date:").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(1).Column(3).Element(CellStyle).ExtendHorizontal().PaddingLeft(5).Text($"{_model.PanCardNumber}").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(2).Column(3).Element(CellStyle).ExtendHorizontal().PaddingLeft(5).Text($"{_model.GstNumber}").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(3).Column(3).Element(CellStyle).ExtendHorizontal().AlignCenter().Text($"Submitted").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(1).Column(4).Element(CellStyle).ExtendHorizontal().AlignLeft().Text("Verified").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(2).Column(4).Element(CellStyle).ExtendHorizontal().AlignLeft().Text("Verified").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(3).ColumnSpan(4).Element(CellStyle).ExtendHorizontal().AlignRight().Text("").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(4).ColumnSpan(4).Element(CellStyle).ExtendHorizontal().AlignRight().Text("").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(5).ColumnSpan(4).Element(CellStyle).ExtendHorizontal().AlignLeft().Text("").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(6).ColumnSpan(4).Element(CellStyle).ExtendHorizontal().AlignLeft().Text("").FontSize(10).FontFamily("Cambria");
  //  table.Cell().Row(3).ColumnSpan(4).Element(CellStyle).ExtendHorizontal().AlignCenter().PaddingRight(30f).Text("Submitted").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(4).Column(3).Element(CellStyle).ExtendHorizontal().AlignCenter().Text(_model.FileExists ? "Submitted" : "NotSubmitted").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(5).Column(3).Element(CellStyle).ExtendHorizontal().AlignCenter().Text(_model.CommencemenceFileExists ? "Submitted" : "NotSubmitted").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(6).Column(3).Element(CellStyle).ExtendHorizontal().AlignCenter().Text("Submitted").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(8).Column(3).Element(CellStyle).AlignLeft().Text("TransactionId:- " + _model.TransactionId).FontSize(10).FontFamily("Cambria");
  table.Cell().Row(8).Column(4).Element(CellStyle).AlignLeft().Text("Date :" + _model.DateOfPayment.ToString("dd-MM-yyyy")).FontSize(10).FontFamily("Cambria");
  table.Cell().RowSpan(2).ColumnSpan(2).Element(CellStyle).ExtendHorizontal().AlignLeft().Text(_model.ReceiptNumber).Bold().FontSize(10).FontFamily("Cambria");

  table.Cell().Row(3).Column(1).Element(CellStyle2).Text("3.").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(4).Column(1).Element(CellStyle2).Text("4.").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(5).Column(1).Element(CellStyle2).Text("5.").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(6).Column(1).Element(CellStyle2).Text("6.").FontSize(10).FontFamily("Cambria");

  table.Cell().Row(10).Column(3).Element(CellStyle).ExtendHorizontal().AlignLeft().Text(_model.ApplicationId).FontSize(10).FontFamily("Cambria");
  table.Cell().Row(10).Column(4).Element(CellStyle).ExtendHorizontal().AlignRight().Text("").FontSize(10).FontFamily("Cambria");
  table.Cell().Row(10).Column(4).Element(CellStyle).ExtendHorizontal().AlignLeft().Text(date.ToString("dd-MM-yyyy")).FontSize(10).FontFamily("Cambria");

  IContainer CellStyle(IContainer container)
  {
    return DefaultCellStyle(container, "#ffffff").ShowOnce();
  }
});
















       column.Item()
             .PaddingVertical(20)
           .AlignLeft()
            .Text(text =>
            {

              text.Span($" Submitting the application for your Consideration & Recommendation").FontSize(10)
                         .FontSize(10).Bold()
                         .FontFamily("Cambria");
              text.Line($"").FontSize(10)
                         .FontSize(10).Bold()
                         .FontFamily("Cambria");

            });
       column.Item().Row(row =>
                                           {
                                             if (_model.IsScrutinizer || _model.VerifiedBySignature != null)
                                             {
                                               row.RelativeItem(1)
                                   .Width(2 * 20)
                                   .Height(2 * 20)
                                   .Image(new BinaryReader(_model.VerifiedBySignature).ReadBytes((int)_model.VerifiedBySignature.Length), ImageScaling.Resize);
                                             }
                                             if (_model.IsConvernor || _model.ConvernorSignature != null)
                                             {
                                               row.RelativeItem(1).AlignCenter()
                                    // .PaddingRight(70)
                                    .Width(2 * 20)
                                    .Height(2 * 20)
                                    .Image(new BinaryReader(_model.ConvernorSignature).ReadBytes((int)_model.ConvernorSignature.Length), ImageScaling.Resize);
                                             }
                                           });
       column.Item().Row(row =>
               {
                 row.RelativeItem().AlignLeft().Text(text =>
                               {
                                 text.Line(_model.CheckedBy)
                                   .FontFamily("Cambria")
                                   .FontSize(9).Bold(); ;
                                 text.Line("Scrutinized By")
                                 .FontFamily("Cambria")
                                 .FontSize(9).Bold(); ;
                               });
                 row.RelativeItem()
                               .AlignCenter().Text(text =>
                               {
                                 text.Line(_model.Chairman)
                                   .FontFamily("Cambria")
                                   .FontSize(9).Bold(); ;
                                 text.Line("Convener, Membership Development Committee")
                                 .FontFamily("Cambria")
                                 .FontSize(9).Bold(); ;
                               });

               });




       column.Item().Row(row =>
                                   {
                                     if (_model.IsDirectorGeneral || _model.DgDesignateSignature != null)
                                     {
                                       row.RelativeItem(1)
                           .Width(2 * 20)
                           .Height(2 * 20)
                           .Image(new BinaryReader(_model.DgDesignateSignature).ReadBytes((int)_model.DgDesignateSignature.Length), ImageScaling.Resize);

                                       row.RelativeItem(1).AlignCenter()
                            // .PaddingRight(70)
                            .Width(2 * 20)
                            .Height(2 * 20)
                            .Image(new BinaryReader(_model.DgSignature).ReadBytes((int)_model.DgSignature.Length), ImageScaling.Resize);
                                     }
                                   });


       column.Item().Row(row =>
                    {
                      row.RelativeItem().Text(text =>
                                    {
                                      text.Line(_model.Director)
                                   .FontFamily("Cambria")
                                   .FontSize(9).Bold();
                                      text.Line("General Manager")
                                 .FontFamily("Cambria")
                                 .FontSize(9).Bold(); ;
                                    });
                      row.RelativeItem()
                                    .AlignCenter().Text(text =>
                                    {
                                      text.Line(_model.DirectorGeneral)
                                   .FontFamily("Cambria")
                                   .FontSize(9).Bold(); ;
                                      text.Line("Director General")
                                 .FontFamily("Cambria")
                                 .FontSize(9).Bold();
                                    });

                    });



       column.Item().Row(row => row.RelativeItem(2f).BorderTop(1f));

       column.Item().PaddingLeft(20f).PaddingRight(20f).AlignLeft().Text(text =>

                     {
                       text.Line("Approved in the MCM dated :  " + _model.ApprovedMcmDate)
                             .FontFamily("Cambria")
                             .FontSize(10).Bold();
                       text.Span("Membership No. allotted :")
                         .FontFamily("Cambria")
                         .FontSize(10).Bold();
                       text.Span(_model.MembershipId)
                           .FontFamily("Cambria").FontColor("#ff0000")
                           .FontSize(10).Bold();
                     });









       //   column.Item().Row(row =>
       //                           {
       //                              row.RelativeItem()
       //                             .AlignRight().Text(text =>
       //                             {
       //                               text.Line(_model.Secretary)
       //                               .FontFamily("Cambria")
       //                               .FontSize(9).Bold();
       //                             text.Line("Hon. Secretary")
       //                               .FontFamily("Cambria")
       //                               .FontSize(9).Bold();
       //                           });
       column.Item().Row(row =>
                                          {

                                            if (_model.SecratorySignature != null)
                                            {
                                              row.RelativeItem().AlignRight()
                                  .Width(4 * 20)
                                  .Height(3 * 20)
                                  .Image(new BinaryReader(_model.SecratorySignature).ReadBytes((int)_model.SecratorySignature.Length), ImageScaling.Resize);
                                            }
                                            else
                                            {
                                              row.RelativeItem()
                                                                    .AlignRight().Text(text =>
                                                                    {
                                                                      text.Line(_model.Secretary)
                                                                .FontFamily("Cambria")
                                                                .FontSize(9).Bold();
                                                                      text.Line("Hon. Secretary")
                                                             .FontFamily("Cambria")
                                                             .FontSize(9).Bold();
                                                                    });
                                            }
                                          });
       static IContainer DefaultCellStyle(IContainer container, string backgroundColor)
       {
         return container.Border(1f).PaddingHorizontal(1f).AlignLeft();
       }



       static IContainer DefaultCellStyleForSno(IContainer container, string backgroundColor)
       {
         return container.Border(1f).PaddingHorizontal(1f).AlignCenter();
       }

       static IContainer CellStyle2(IContainer container)
       {
         return DefaultCellStyleForSno(container, "#ffffff").ShowOnce();
       }
     });
    }

    private async Task<string> GetFinancialYear()
    {
      int startMonthNumber = GetMonthNumber("April");
      int endMonthNumber = GetMonthNumber("March");
      DateTime currentDate = TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, TimeZoneInfo.FindSystemTimeZoneById("Asia/Kolkata"));
      string startYear = currentDate.ToString("yyyy");
      string endYear;

      if (currentDate.Month < startMonthNumber)
      {
        startYear = currentDate.AddYears(-1).ToString("yyyy");
        endYear = currentDate.ToString("yyyy");
      }
      else
      {
        endYear = currentDate.AddYears(1).ToString("yyyy");
      }

      string financialYear = startYear.Substring(2) + "-" + endYear.Substring(2);

      return financialYear;
    }

    private static int GetMonthNumber(string month)
    {
      string lower = month.ToLower();
      if (lower != null)
      {
        switch (lower.Length)
        {
          case 3:
            if (lower == "may")
              return 5;
            break;
          case 4:
            switch (lower[2])
            {
              case 'l':
                if (lower == "july")
                  return 7;
                break;
              case 'n':
                if (lower == "june")
                  return 6;
                break;
            }
            break;
          case 5:
            switch (lower[0])
            {
              case 'a':
                if (lower == "april")
                  return 4;
                break;
              case 'm':
                if (lower == "march")
                  return 3;
                break;
            }
            break;
          case 6:
            if (lower == "august")
              return 8;
            break;
          case 7:
            switch (lower[0])
            {
              case 'j':
                if (lower == "january")
                  return 1;
                break;
              case 'o':
                if (lower == "october")
                  return 10;
                break;
            }
            break;
          case 8:
            switch (lower[0])
            {
              case 'd':
                if (lower == "december")
                  return 12;
                break;
              case 'f':
                if (lower == "february")
                  return 2;
                break;
              case 'n':
                if (lower == "november")
                  return 11;
                break;
            }
            break;
          case 9:
            if (lower == "september")
              return 9;
            break;
        }
      }
      throw new ArgumentException("Invalid month: " + month);
    }

    // void ComposeReraHeader(IContainer container)
    // {
    //     container.Column(column =>
    //     {
    //         // var stream = Assembly.GetExecutingAssembly().GetManifestResourceStream("recommendedPlugin.Logo.png");
    //         // if (stream != null)
    //         // {

    //         //     column.Item().Row(row =>
    //         //     {
    //         //         row.RelativeItem(4);
    //         //         row.RelativeItem(1)
    //         //             .AlignCenter()
    //         //             .Height(50)
    //         //             .Image(new BinaryReader(stream).ReadBytes((int)stream.Length), ImageScaling.Resize);
    //         //         row.RelativeItem(4);
    //         //     });
    //         // }

    //     });
    // }
    void ComposeReraContent1(IContainer container)
    {
      container.Column(column =>
     {
       column.Item()
           .AlignLeft()
           .PaddingLeft(20)
           .PaddingRight(20)
              .Text(text =>
              {

                text.Span($"Date: {_model.Date}{Environment.NewLine}").FontSize(10)
                          .FontFamily("Cambria");

              });
       column.Item()

          .PaddingLeft(20)
          .PaddingRight(20)
            .AlignLeft()
          .Text(text =>
          {

            text.Span("Office Note:").FontSize(10)
                      .FontFamily("Cambria");


          });
       column.Item()

          .PaddingLeft(20)
          .PaddingRight(20)
           .AlignLeft()
          .Text(text =>
          {

            text.Span("Dr.D.K. Abhyankar").FontSize(10)
                      .FontFamily("Cambria");

          });
       column.Item()

            .PaddingLeft(20)
            .PaddingRight(20)
            .AlignLeft()
            .Text(text =>
            {

              text.Span($"Director General{Environment.NewLine}").FontSize(10)
                        .FontFamily("Cambria");

            });

       column.Item()
            .PaddingLeft(20)
            .AlignLeft()
             .Text(text =>
             {

               text.Span("Sub: CREDAI-Pune ").FontSize(10)
                          .FontFamily("Cambria");
               text.Span(" RERA Project Membership Application").FontSize(10)
                       .FontSize(10).Bold()
                       .FontFamily("Cambria");
               text.Span($" for approval.{Environment.NewLine}").FontSize(10)
                      .FontFamily("Cambria");
             });

       column.Item()
             .PaddingLeft(20)
             .PaddingRight(20)
             .AlignLeft()
             .Text(text =>
             {

               text.Span("With reference to above, we are submitting herewith following application for RERA Project Membership along with the documents submitted by them for your kind consideration. The details of the project applied are as below :").FontSize(10)
                     .FontFamily("Cambria");

             });
       column.Item().PaddingLeft(20)
                                   .MinimalBox()
                                  .Border(1)

                                          .Table(table =>
                                          {
                                            IContainer DefaultCellStyle(IContainer cellContainer, string backgroundColor)
                                            {
                                              return cellContainer.Border(1)
               //  .BorderColor(Colors.Grey.Lighten1)
               //  .Background(backgroundColor)
               .PaddingHorizontal(1)
               .AlignLeft();

                                            }

                                            //  table.ColumnsDefinition(columns =>
                                            //  {
                                            //      columns.RelativeColumn(2);
                                            //  });
                                            table.ColumnsDefinition(columns =>
                               {
                                 columns.ConstantColumn(200);
                                 columns.ConstantColumn(260);
                                 //  columns.RelativeColumn();
                               });
                                            table.Header(header =>
                                                      {
                                                        header.Cell().RowSpan(2).Element(CellStyle).AlignCenter().Text("Name of the Project").FontFamily("Cambria").FontSize(10).Bold();
                                                        header.Cell().RowSpan(2).Element(CellStyle).AlignCenter().Text(_model.ProjectName).FontFamily("Cambria").FontSize(10);
                                                        header.Cell().Element(CellStyle).Text("Project address Location").FontFamily("Cambria").FontSize(10).Bold();
                                                        header.Cell().Element(CellStyle).Text(_model.Location).FontFamily("Cambria").FontSize(10);
                                                        header.Cell().Element(CellStyle).Text("Proposed date of Completion").FontFamily("Cambria").FontSize(10).Bold();
                                                        header.Cell().Element(CellStyle).Text(_model.DateOfCompletion).FontFamily("Cambria").FontSize(10);
                                                        header.Cell().ColumnSpan(1).Element(CellStyle).Text("No. of unit in Project").FontFamily("Cambria").FontSize(10).Bold();
                                                        header.Cell().ColumnSpan(1).Element(CellStyle).Text($"Residential:{_model.NoOfResidentailProject}, Commertial :{_model.NoOfCommercialProject}, Plot :{_model.NoOfPlotProject}").FontFamily("Cambria").FontSize(10).Bold();
                                                        //                             header.Cell().ColumnSpan(1).Element(CellStyle).Text(text =>
                                                        //                             {

                                                        //                                 text.Line($"").FontSize(10)
                                                        // .FontFamily("Cambria");
                                                        //                                 text.Span($" ").FontSize(10)
                                                        //         .FontFamily("Cambria");
                                                        //                                 text.Span($"").FontSize(10)
                                                        //                                .FontFamily("Cambria");


                                                        // });



                                                        //  );
                                                        header.Cell().ColumnSpan(1).Element(CellStyle).Text("Name of the Applicant Firm").FontFamily("Cambria").FontSize(10).Bold();
                                                        header.Cell().Element(CellStyle).Text($"{_model.Firm}").FontFamily("Cambria").FontSize(10);
                                                        header.Cell().Element(CellStyle).Text("Address of the firm").FontFamily("Cambria").FontSize(10).Bold();
                                                        header.Cell().Element(CellStyle).Text($"{_model.FirmAddress}").FontFamily("Cambria").FontSize(10);
                                                        header.Cell().Element(CellStyle).Text("Name of the Partners/Proprietor/Directors/Members").FontFamily("Cambria").FontSize(10).Bold();
                                                        string[] elements = _model.ProprietorOrPartnerNameOrDirectors.Split(',');
                                                        string concatenatedElements = "";
                                                        int elementsPerRow = 3;

                                                        if (elements.Length <= elementsPerRow)
                                                        {
                                                          concatenatedElements = string.Join("\n", elements);
                                                        }
                                                        else
                                                        {

                                                          for (int i = 0; i < elements.Length; i += elementsPerRow)
                                                          {
                                                            string[] rowElements = elements.Skip(i).Take(elementsPerRow).ToArray();
                                                            concatenatedElements += string.Join(", ", rowElements) + "\n";
                                                          }
                                                        }

                                                        header.Cell().Element(CellStyle).Text($"{concatenatedElements}").FontFamily("Cambria").FontSize(10);
                                                        IContainer CellStyle(IContainer container) => DefaultCellStyle(container, Colors.Grey.Lighten3);
                                                      });
                                            IContainer CellStyle(IContainer cellContainer) => DefaultCellStyle(cellContainer, Colors.White).ShowOnce();
                                          });
       //    column.Item()
       //    .PaddingLeft(20)
       //    .PaddingRight(20)
       //   .AlignLeft()
       //    .Text(text =>
       //    {

       //        text.Span($"{Environment.NewLine}Any dues from Ordinary Member for the F Y 2023-2024: Yes/No {Environment.NewLine}").FontSize(10)
       //             .FontSize(10).Bold()
       //             .FontFamily("Cambria");

       //    });
       column.Item()
            .PaddingLeft(20)
            .PaddingTop(10)
            .PaddingRight(20)
           .AlignLeft()
            .Text(text =>
            {
              text.Span($"The firm has submitted following photocopies of the documents along with the application:").FontSize(10)
                         .FontFamily("Cambria");
            });
       column.Item()
.PaddingLeft(20)
           .MinimalBox()
           .Border(1)
           .Table(table =>
 {
   IContainer DefaultCellStyle(IContainer container, string backgroundColor)
   {
     return container
               .Border(1)
               //  .BorderColor(Colors.Grey.Lighten1)
               //  .Background(backgroundColor)
               .PaddingHorizontal(1)
               .AlignLeft();




   }
   IContainer DefaultCellStyleForSno(IContainer container, string backgroundColor)
   {
     return container
               .Border(1)
               //  .BorderColor(Colors.Grey.Lighten1)
               //  .Background(backgroundColor)
               .PaddingHorizontal(1)
               .AlignCenter();




   }

   table.ColumnsDefinition(columns =>
        {
          columns.ConstantColumn(40);

          columns.ConstantColumn(150);
          columns.ConstantColumn(170);

          columns.ConstantColumn(100);



        });




   table.Header(header =>
        {
          // please be sure to call the 'header' handler!

          header.Cell().RowSpan(2).Element(CellStyle).ExtendHorizontal().AlignLeft().Text("");
          header.Cell().ColumnSpan(1).AlignCenter().Text("Document").FontSize(12).FontFamily("Cambria").Bold();
          //    table.Cell().Row().Column(4).Element(CellStyle).ExtendHorizontal().AlignRight().Text("").FontSize(10).FontFamily("Cambria");
          header.Cell().ColumnSpan(2).Element(CellStyle).AlignCenter().PaddingLeft(100).Text("Remarks").FontSize(12).FontFamily("Cambria").Bold();

          // you can extend existing styles by creating additional methods
          IContainer CellStyle(IContainer container) => DefaultCellStyle(container, Colors.Grey.Lighten3);
        });
   table.Cell().Row(1).Column(1).Element(CellStyle2).Text("1.").FontSize(10).FontFamily("Cambria");
   table.Cell().Row(2).Column(1).Element(CellStyle2).Text("2.").FontSize(10).FontFamily("Cambria");
   table.Cell().Row(7).Column(1).Element(CellStyle2).Text("7.").FontSize(10).FontFamily("Cambria");
   //  table.Cell().Row(8).Column(1);
   //  table.Cell().Row(10).Column(1);
   table.Cell().Row(10).Column(1).Element(CellStyle2).Text("8.").FontSize(10).FontFamily("Cambria");
   table.Cell().Row(1).Column(2).Element(CellStyle).ExtendHorizontal().AlignLeft().Text("PAN Card No").FontSize(10).FontFamily("Cambria");
   table.Cell().Row(2).Column(2).Element(CellStyle).ExtendHorizontal().AlignLeft().Text("GST No.").FontSize(10).FontFamily("Cambria");
   table.Cell().Row(3).Column(2).Element(CellStyle).ExtendHorizontal().AlignLeft().Text("Copy of Aadhaar/PAN Card of the Proprietor/Partners").FontSize(10).FontFamily("Cambria");
   table.Cell().Row(4).Column(2).Element(CellStyle).ExtendHorizontal().AlignLeft().Text("Partnership Deed/MOA/Articles of Association/Shop Act Licence/AOP Agreement/LLP Agreement").FontSize(10).FontFamily("Cambria");
   table.Cell().Row(5).Column(2).Element(CellStyle).ExtendHorizontal().AlignLeft().Text("Commencement/NA order").FontSize(10).FontFamily("Cambria");
   table.Cell().Row(6).Column(2).Element(CellStyle).ExtendHorizontal().AlignLeft().Text("Copy of Code of Conduct ").FontSize(10).FontFamily("Cambria");
   table.Cell().Row(7).Column(2).AlignLeft().Text("Payment Details").Bold().FontSize(10).FontFamily("Cambria");
   table.Cell().Row(8).Column(2).Element(CellStyle).ExtendHorizontal().AlignLeft().Text($"Amount:- Rs. {_model.AmountPaid}/-").FontSize(10).FontFamily("Cambria");
   table.Cell().Row(10).Column(2).Element(CellStyle).ExtendHorizontal().AlignLeft().Text("R.No.").Bold().FontSize(10).FontFamily("Cambria");
   table.Cell().Row(10).Column(2).Element(CellStyle).ExtendHorizontal().AlignLeft().Text($"Inward No & Date:").FontSize(10).FontFamily("Cambria");

   table.Cell().Row(1).Column(3).Element(CellStyle).ExtendHorizontal().PaddingLeft(5).Text($"{_model.PanCardNumber}").FontSize(10).FontFamily("Cambria");
   table.Cell().Row(2).Column(3).Element(CellStyle).ExtendHorizontal().PaddingLeft(5).Text($"{_model.GstNumber}").FontSize(10).FontFamily("Cambria");
   table.Cell().Row(1).Column(4).Element(CellStyle).ExtendHorizontal().AlignLeft().Text("Verified").FontSize(10).FontFamily("Cambria");
   table.Cell().Row(2).Column(4).Element(CellStyle).ExtendHorizontal().AlignLeft().Text("Verified").FontSize(10).FontFamily("Cambria");
   table.Cell().Row(3).Column(4).Element(CellStyle).ExtendHorizontal().AlignRight().Text("").FontSize(10).FontFamily("Cambria");
   table.Cell().Row(4).Column(4).Element(CellStyle).ExtendHorizontal().AlignRight().Text("").FontSize(10).FontFamily("Cambria");
   table.Cell().Row(5).Column(4).Element(CellStyle).ExtendHorizontal().AlignCenter().Text("Verified").FontSize(10).FontFamily("Cambria");
   //  table.Cell().Row(6).Column(4).Element(CellStyle).ExtendHorizontal().AlignRight().Text("NotVerified").FontSize(10).FontFamily("Cambria");
   //  table.Cell().Row(7).Column(4).Element(CellStyle).ExtendHorizontal().AlignRight().Text("NotVerified").FontSize(10).FontFamily("Cambria");
   //  table.Cell().Row(8).Column(4).Element(CellStyle).ExtendHorizontal().AlignRight().Text("").FontSize(10).FontFamily("Cambria");









   table.Cell().Row(3).ColumnSpan(4).Element(CellStyle).ExtendHorizontal().AlignCenter().PaddingRight(30).Text("Submitted").FontSize(10).FontFamily("Cambria");
   table.Cell().Row(4).ColumnSpan(4).Element(CellStyle).ExtendHorizontal().AlignCenter().PaddingRight(30).Text("Submitted").FontSize(10).FontFamily("Cambria");
   table.Cell().Row(5).ColumnSpan(4).Element(CellStyle).ExtendHorizontal().AlignCenter().PaddingRight(30).Text("").FontSize(10).FontFamily("Cambria");
   table.Cell().Row(6).ColumnSpan(4).Element(CellStyle).ExtendHorizontal().AlignCenter().PaddingRight(30).Text("Submitted").FontSize(10).FontFamily("Cambria");
   table.Cell().Row(8).Column(3).Element(CellStyle).ExtendHorizontal().AlignLeft().Text("").FontSize(10).FontFamily("Cambria");
   table.Cell().Row(8).Column(4).Element(CellStyle).ExtendHorizontal().AlignLeft().Text($"Date :{_model.DateOfPayment}").Bold().FontSize(10).FontFamily("Cambria");
   table.Cell().RowSpan(2).ColumnSpan(2).Element(CellStyle).ExtendHorizontal().AlignLeft().Text($"Date of Payment Realization:- {_model.DateOfPayment}").Bold().FontSize(10).FontFamily("Cambria");
   table.Cell().Row(3).Column(1).Element(CellStyle2).Text("3.").FontSize(10).FontFamily("Cambria");
   table.Cell().Row(4).Column(1).Element(CellStyle2).Text("4.").FontSize(10).FontFamily("Cambria");
   table.Cell().Row(5).Column(1).Element(CellStyle2).Text("5.").FontSize(10).FontFamily("Cambria");
   table.Cell().Row(6).Column(1).Element(CellStyle2).Text("6.").FontSize(10).FontFamily("Cambria");
   table.Cell().Row(10).Column(3).Element(CellStyle).ExtendHorizontal().AlignRight().Text("").FontSize(10);
   table.Cell().Row(10).Column(4).Element(CellStyle).ExtendHorizontal().AlignRight().Text("").FontSize(10);
   table.Cell().Row(10).Column(4).Element(CellStyle).ExtendHorizontal().AlignLeft().Text($"{_model.DateOfPayment}").FontSize(10).FontFamily("Cambria");



   IContainer CellStyle(IContainer container) => DefaultCellStyle(container, Colors.White).ShowOnce();
   IContainer CellStyle2(IContainer container) => DefaultCellStyleForSno(container, Colors.White).ShowOnce();

 });
       column.Item()
             .PaddingVertical(20)
           .AlignLeft()
            .Text(text =>
            {

              text.Span($"Submitting the application for your Consideration & Recommendation").FontSize(10)
                         .FontSize(10).Bold()
                         .FontFamily("Cambria");

            });

       column.Item().PaddingLeft(20).PaddingRight(20).Row(row =>
               {
                 row.RelativeItem(1)
                               .AlignLeft().Text(text =>
                               {
                                 text.Span($"{_model.PreparedBy}{Environment.NewLine}")
                                   .FontFamily("Cambria")
                                   .FontSize(10);
                                 text.Span("Prepared By")
                                 .FontFamily("Cambria")
                                 .FontSize(10);
                               });
                 //  row.RelativeItem(1)
                 //          .AlignRight().Text(text =>
                 //          {
                 //              text.Span($"Mr. Mayur Ekbote{Environment.NewLine}")
                 //              .FontFamily("Cambria")
                 //              .FontSize(10);
                 //              text.Span("Account Checked By")
                 //            .FontFamily("Cambria")
                 //            .FontSize(10);
                 //          });

               });
       column.Item().PaddingLeft(20).PaddingRight(20).PaddingVertical(10).Row(row =>
            {
              row.RelativeItem(1)
                            .AlignLeft().Text(text =>
                            {
                              text.Span($"{_model.CheckedBy}{Environment.NewLine}")
                                .FontFamily("Cambria")
                                .FontSize(10);
                              text.Span("Checked By")
                              .FontFamily("Cambria")
                              .FontSize(10);
                            });
              row.RelativeItem(1)
                            .AlignRight().Text(text =>
                            {
                              text.Span($"{_model.DirectorGeneral}{Environment.NewLine}")
                                .FontFamily("Cambria")
                                .FontSize(10);
                              text.Span($"Director General{Environment.NewLine}")
                              .FontFamily("Cambria")
                              .FontSize(10);
                            });

            });

       column.Item()
            .AlignCenter()
           .Text(text =>
        {

          text.Span($"{_model.Chairman}").FontSize(10)
               .FontSize(10).Bold()
               .FontFamily("Cambria");


        });
       column.Item()

               .AlignCenter()
               .Text(text =>
            {


              text.Span($"Chairman, Membership Development Committee{Environment.NewLine}").FontSize(10)
                  .FontSize(10).Bold()
                  .FontFamily("Cambria");
            });

       column.Item().Row(row =>
             {
               row.RelativeItem(2)
                                    .BorderTop(1);
             });
       column.Item()
            .PaddingLeft(20)
            .PaddingRight(20)
           //   .PaddingVertical(10)
           .AlignLeft()
            .Text(text =>
            {

              text.Span($"Approved in the MCM dated :  {_model.ApprovedMcmDate}                                   {Environment.NewLine}{Environment.NewLine}").FontSize(10)
                         .FontSize(10).Bold()
                         .FontFamily("Cambria");
              text.Span($"Membership No. allotted :").FontSize(10)
                   .FontSize(10).Bold()
                   .FontFamily("Cambria");
              text.Span($"{_model.ApplicantId}").FontSize(10)
              .FontColor("#F00")
                   .FontSize(10).Bold()
                   .FontFamily("Cambria");

            });
       //    column.Item()
       //   .PaddingLeft(20)
       //   .PaddingRight(20)
       //  .AlignLeft()
       //   .Text(text =>
       //   {



       //   });
       column.Item().PaddingTop(30)
                             .Row(row =>
                             {
                               row.RelativeItem(1)
                                             .AlignCenter()
                .Text(text =>
                {

                  text.Span($"{_model.Secretary}").FontSize(10)
                         .FontSize(10).Bold()
                         .FontFamily("Cambria");

                });
                               row.RelativeItem(1)
                                                                 .AlignCenter()
                                .Text(text =>
                                {

                                  text.Span($"{_model.President}").FontSize(10)
                                         .FontSize(10).Bold()
                                         .FontFamily("Cambria");





                                });
                             });

       column.Item()
                                          .Row(row =>
                                          {
                                            row.RelativeItem(1)
                                                          .AlignCenter()
                             .Text(text =>
                             {

                               text.Span($"Hon. Secretary").FontSize(10)
                                      .FontSize(10).Bold()
                                      .FontFamily("Cambria");

                             });
                                            row.RelativeItem(1)
                                                                              .AlignCenter()
                                             .Text(text =>
                                             {

                                               text.Span($"President").FontSize(10)
                                                      .FontSize(10).Bold()
                                                      .FontFamily("Cambria");





                                             });
                                          });

















       //                   column.Item()
       //  .PaddingLeft(20)
       //  .PaddingRight(20)
       // .AlignLeft()
       //  .Text(text =>
       //  {

       //                   text.Span($"Hon. Secretary").FontSize(10)
       //                        .FontSize(10).Bold()
       //                        .FontFamily("Cambria");

       //               });
       //                column.Item()
       // //   .PaddingLeft(20)
       // //   .PaddingRight(20)
       // //   .PaddingTop(20)
       // .AlignRight()
       // .Text(text =>
       // {

       //     text.Span($"fwefwefwe").FontSize(10)
       //          .FontSize(10).Bold()
       //          .FontFamily("Cambria");

       // });
       //                column.Item()
       // //   .PaddingLeft(20)
       // //   .PaddingRight(20)
       // .AlignRight()
       // .Text(text =>
       // {

       //     text.Span($"President").FontSize(10)
       //          .FontSize(10).Bold()
       //          .FontFamily("Cambria");

       // });

     });
    }















  }

  public static class Mapper
  {
    public static T Map<T>(ExpandoObject source) where T : class
    {
      T destination = (T)Activator.CreateInstance(typeof(T));
      IDictionary<string, object> dict = source;
      var type = destination.GetType();

      foreach (var prop in type.GetProperties())
      {
        var lower = prop.Name.ToLower();
        Console.WriteLine($"Mapping {lower}");
        var key = dict.Keys.SingleOrDefault(k => k.ToLower() == lower);

        if (key != null)
        {
          prop.SetValue(destination, CastProperty(dict[key], prop.PropertyType.ToString()), null);
        }
      }
      return destination;
    }

    static dynamic CastProperty(dynamic property, string type)
    {
      switch (type)
      {
        case "System.String":
          return (String)property;
        case "System.Decimal":
          return (Decimal)property;
        case "System.DateTime":
          return Convert.ToDateTime(property);
        default:
          return property;

      }
    }
    public class Model
    {
      public string ApplicantId { get; set; }

      public dynamic ConvernorSignature { get; set; }

      public bool IsScrutinizer { get; set; }

      public bool IsConvernor { get; set; }

      public bool IsDirectorGeneral { get; set; }

      public dynamic SecratorySignature { get; set; }

      public dynamic DgSignature { get; set; }

      public dynamic DgDesignateSignature { get; set; }

      public dynamic VerifiedBySignature { get; set; }

      public string TransactionId { get; set; }

      public string MembershipId { get; set; }

      public string Director { get; set; }

      public string Type { get; set; }

      public DateTime Date { get; set; } = TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, TimeZoneInfo.FindSystemTimeZoneById("Asia/Kolkata")).Date;

      public string Address { get; set; }

      public string Name { get; set; }

      public string ApprovedMcmDate { get; set; }

      public string AmountPaid { get; set; }

      public string ProprietorOrPartnerNameOrDirectors { get; set; }

      public string PanCardNumber { get; set; }

      public string GstNumber { get; set; }

      public string OrdinaryMemberName { get; set; }

      public DateTime DateOfPayment { get; set; }

      public string ApplicationId { get; set; }

      public DateTime CreatedDate { get; set; }

      public string PartnerName { get; set; }

      public string Amt { get; set; }

      public string ChqNo { get; set; }

      public string BuilderName { get; set; }

      public string ProjectName { get; set; }

      public string Location { get; set; }

      public string DateOfCompletion { get; set; }

      public string NoOfResidentailProject { get; set; }

      public string NoOfCommercialProject { get; set; }

      public string NoOfPlotProject { get; set; }

      public string Firm { get; set; }

      public string FirmAddress { get; set; }

      public string ProposerName { get; set; }

      public string SecondaryName { get; set; }

      public string CheckedBy { get; set; }

      public string PreparedBy { get; set; }

      public string DirectorGeneral { get; set; }

      public string Chairman { get; set; }

      public string President { get; set; }

      public string Secretary { get; set; }

      public string ReceiptNumber { get; set; }

      public string SecondaryFirmName { get; set; }

      public string ProposerFirmName { get; set; }

      public bool FileExists { get; set; }

      public bool CommencemenceFileExists { get; set; }
    }


  }
}
