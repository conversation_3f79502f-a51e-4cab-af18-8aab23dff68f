﻿using System.Collections.Generic;
using System.Net;
using System.Net.Mail;
using Factory.Plugins;
using Factory.Models;
using System.Dynamic;
using System.Text;
using System.Text.Json;

namespace Factory.HttpPlugin
{

    public class HttpPlugin : IRemoteCallPlugin
    {
        private static List<string> Methods = new List<string>()
        {
            "GET", "POST", "PUT", "PATCH", "DELETE"
        };

        public string GetName()
        {
            return "Http";
        }

        public List<string> GetMethods()
        {
            return Methods;
        }

        public string GetHelp()
        {
            return "";
        }

        public List<FieldMeta> GetConfigurationMeta()
        {
            return new List<FieldMeta>() {
                new FieldMeta() {
                    Name = "BaseUrl",
                    Category = FieldCategory.Primitive,
                    IsCollection = false,
                    IsRequired = true,
                    IsUnique = false,
                    StorageType = StorageType.Plain,
                    Type = PrimitiveType.Text.ToString()
                }
            };
        }

        private string _baseUrl { get; set; }
        private List<KeyValuePair<string, string>> _headers { get; set; }

        public void Configure(dynamic configuration)
        {

            _baseUrl = ((IDictionary<string, object>)configuration)["BaseUrl"].ToString();
        }

        public async Task<dynamic> Invoke(dynamic input)
        {
            var model = Mapper.Map<Model>(input);
            dynamic output = new ExpandoObject();

            using (var httpClient = new HttpClient())
            {
                httpClient.Timeout = TimeSpan.FromSeconds(200);
                var requestMessage = new HttpRequestMessage();
                requestMessage.Method = GetMethod(model.Method);
                //  requestMessage.RequestUri = new Uri($"https://enfbzr2ju8ea.x.pipedream.net/");
                if (model.Path.StartsWith("?"))
                {

                    requestMessage.RequestUri = new Uri($"{_baseUrl}{model.Path}");
                }
                else
                {
                    requestMessage.RequestUri = new Uri($"{_baseUrl}/{model.Path}");
                }
                if (model.Headers == "Content-Type: application/x-www-form-urlencoded")
                {
                    KeyValuePair<string, string>[] deserializedFormFields = JsonSerializer.Deserialize<KeyValuePair<string, string>[]>(model.Body);

                    FormUrlEncodedContent formContent = new FormUrlEncodedContent(deserializedFormFields);
                    requestMessage.Content = formContent;
                }
                else
                {
                    if (model.Body != null)
                    {

                        requestMessage.Content = new StreamContent(new MemoryStream(model.Body));
                    }
                    else
                    {
                        requestMessage.Content = new StreamContent(new MemoryStream(new byte[0]));
                    }
                }
                ApplyHeaders(requestMessage, model.Headers);

                HttpResponseMessage response = await httpClient.SendAsync(requestMessage, HttpCompletionOption.ResponseHeadersRead);
                output.Status = (int)response.StatusCode;
                output.Content = await response.Content.ReadAsByteArrayAsync();
                output.Headers = RetrieveHeaders(response);

            }
            return output;
        }

        public void ApplyHeaders(HttpRequestMessage requestMessage, string headers)
        {
            foreach (string header in headers.Split("\r\n"))
            {
                var headerTokens = header.Split(":");
                if (headerTokens[0].Trim().Equals("Authorization"))
                {
                    requestMessage.Headers.TryAddWithoutValidation(headerTokens[0].Trim(), headerTokens[1].Trim());
                }
                else
                {
                    requestMessage.Content?.Headers.TryAddWithoutValidation(headerTokens[0].Trim(), headerTokens[1].Trim());
                }

            }
        }

        public string RetrieveHeaders(HttpResponseMessage responseMessage)
        {
            bool first = true;
            var headers = new StringBuilder();
            foreach (var header in responseMessage.Headers)
            {
                if (!first)
                {
                    headers.Append("\r\n");
                }
                else
                {
                    first = false;
                }
                headers.Append(header.Key.Trim()).Append(":").Append(String.Join(";", header.Value.ToArray()));
            }

            foreach (var header in responseMessage.Content.Headers)
            {
                if (!first)
                {
                    headers.Append("\r\n");
                }
                else
                {
                    first = false;
                }
                headers.Append(header.Key).Append(":").Append(String.Join(";", header.Value.ToArray()));
            }
            return headers.ToString();
        }

        private static HttpMethod GetMethod(string method)
        {
            switch (method.ToUpper())
            {
                case "GET":
                    return HttpMethod.Get;
                case "POST":
                    return HttpMethod.Post;
                case "PUT":
                    return HttpMethod.Put;
                case "PATCH":
                    return HttpMethod.Patch;
                case "DELETE":
                    return HttpMethod.Delete;
                case "HEAD":
                    return HttpMethod.Head;
                case "OPTIONS":
                    return HttpMethod.Options;
                case "TRACE":
                    return HttpMethod.Trace;
                default:
                    return new HttpMethod(method);
            }
        }
    }

    public static class Mapper
    {
        public static T Map<T>(ExpandoObject source) where T : class
        {
            T destination = (T)Activator.CreateInstance(typeof(T));
            IDictionary<string, object> dict = source;
            var type = destination.GetType();

            foreach (var prop in type.GetProperties())
            {
                var lower = prop.Name.ToLower();
                Console.WriteLine($"Mapping {lower}");
                var key = dict.Keys.SingleOrDefault(k => k.ToLower() == lower);

                if (key != null)
                {
                    prop.SetValue(destination, CastProperty(dict[key], prop.PropertyType.ToString()), null);
                }
            }
            return destination;
        }

        static dynamic CastProperty(dynamic property, string type)
        {
            switch (type)
            {
                case "System.String":
                    return (String)property;
                case "System.Decimal":
                    return (Decimal)property;
                default:
                    return property;

            }
        }

    }

    public class Model
    {
        public string Method { get; set; }
        public string Path { get; set; }
        public string Headers { get; set; }
        public byte[] Body { get; set; }
    }
}