using System.Collections.Generic;
using System.Net;
using System.Net.Mail;
using Factory.Plugins;
using Factory.Models;

namespace Factory.GlobalSMSPlugin
{

    public class GlobalSMSPlugin : INotificationPlugin
    {
        public string GetName()
        {
            return "Global SMS";
        }
        public List<FieldMeta> GetConfigurationMeta()
        {
            return new List<FieldMeta>() {
                new FieldMeta() {
                    Name = "ApiKey",
                    Category = FieldCategory.Primitive,
                    IsCollection = false,
                    IsRequired = true,
                    IsUnique = false,
                    StorageType = StorageType.Plain,
                    Type = PrimitiveType.Text.ToString()
                }, new FieldMeta() {
                    Name = "SenderId",
                    Category = FieldCategory.Primitive,
                    IsCollection = false,
                    IsRequired = true,
                    IsUnique = false,
                    StorageType = StorageType.Plain,
                    Type = PrimitiveType.Text.ToString()
                }
            };
        }

        private string ApiKey { get; set; }
        private string SenderId { get; set; }

        public void Configure(dynamic configuration)
        {

            ApiKey = ((IDictionary<string, object>)configuration)["ApiKey"].ToString();
            SenderId = ((IDictionary<string, object>)configuration)["SenderId"].ToString();
        }

        public async Task Notify(List<string> toList, string subject, string body, byte[] byteArray = null)
        {
            using (var client = new HttpClient())
            {
                var response = await client.GetAsync($"http://w.global91sms.in/public/sms/send?sender={SenderId}&smstype=TRANS&numbers={string.Join(',', toList)}&apikey={ApiKey}&message={body}");
                if ((int)response.StatusCode >= 400)
                {
                    Console.WriteLine(await response.Content.ReadAsStringAsync());
                }
                else
                {
                    Console.WriteLine(await response.Content.ReadAsStringAsync());
                }
            }
        }

        public string GetHelp()
        {
            return "";
        }
    }
}