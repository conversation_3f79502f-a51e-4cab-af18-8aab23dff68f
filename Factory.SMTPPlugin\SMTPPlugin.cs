﻿using System.Collections.Generic;
using System.Net;
using System.Net.Mail;
using Factory.Plugins;
using Factory.Models;

namespace Factory.SMTPPlugin
{

    public class SMTPPlugin : INotificationPlugin
    {
        public string GetName()
        {
            return "SMTP";
        }
        public List<FieldMeta> GetConfigurationMeta()
        {
            return new List<FieldMeta>()
            {
                //     new FieldMeta() {
                //         Name = "Server",
                //         Category = FieldCategory.Primitive,
                //         IsCollection = false,
                //         IsRequired = true,
                //         IsUnique = false,
                //         StorageType = StorageType.Plain,
                //         Type = PrimitiveType.Text.ToString(),
                //         RegExPattern = "((?!-)[A-Za-z0-9-]{1,63}(?<!-)(?<!-)\\.)+[A-Za-z]{2,6}"
                //     }, new FieldMeta() {
                //         Name = "Port",
                //         Category = FieldCategory.Primitive,
                //         IsCollection = false,
                //         IsRequired = true,
                //         IsUnique = false,
                //         StorageType = StorageType.Plain,
                //         Type = PrimitiveType.Number.ToString(),
                //         MaxValue = "65535",
                //         MinValue = "0"
                //     }, new FieldMeta() {
                //         Name = "Username",
                //         Category = FieldCategory.Primitive,
                //         IsCollection = false,
                //         IsRequired = true,
                //         IsUnique = false,
                //         StorageType = StorageType.Plain,
                //         Type = PrimitiveType.Text.ToString(),
                //         MaxValue = "50",
                //         MinValue = "2"
                //     }, new FieldMeta() {
                //         Name = "Password",
                //         Category = FieldCategory.Primitive,
                //         IsCollection = false,
                //         IsRequired = true,
                //         IsUnique = false,
                //         StorageType = StorageType.Encrypted,
                //         Type = PrimitiveType.Text.ToString(),
                //         MaxValue = "50",
                //         MinValue = "2"
                //     }, new FieldMeta() {
                //         Name = "UseSSL",
                //         Category = FieldCategory.Primitive,
                //         IsCollection = false,
                //         IsRequired = true,
                //         IsUnique = false,
                //         StorageType = StorageType.Plain,
                //         Type = PrimitiveType.Boolean.ToString()
                //     }, new FieldMeta() {
                //         Name = "FromAddress",
                //         Category = FieldCategory.Primitive,
                //         IsCollection = false,
                //         IsRequired = true,
                //         IsUnique = false,
                //         StorageType = StorageType.Plain,
                //         Type = PrimitiveType.Text.ToString(),
                //         MaxValue = "50",
                //         MinValue = "2"
                //     },
                //     new FieldMeta() {
                //         Name = "FromName",
                //         Category = FieldCategory.Primitive,
                //         IsCollection = false,
                //         IsRequired = true,
                //         IsUnique = false,
                //         StorageType = StorageType.Plain,
                //         Type = PrimitiveType.Text.ToString(),
                //         MaxValue = "50",
                //         MinValue = "2"
                //     }
            };
        }

        private string Server { get; set; }
        private int Port { get; set; }
        private string Username { get; set; }
        private string Password { get; set; }
        private string FromAddress { get; set; }
        private string FromName { get; set; }
        private bool EnableSSL { get; set; }

        public void Configure(dynamic configuration)
        {

            Console.WriteLine($"====Condifg={configuration}");
            var dictionary = (IDictionary<string, object>)configuration;
            foreach (var property in dictionary)
            {
                Console.WriteLine($"{property.Key}: {property.Value}");
            }
            Server = ((IDictionary<string, object>)configuration)["Server"].ToString();
            Port = Int32.Parse(((IDictionary<string, object>)configuration)["Port"].ToString());
            Username = ((IDictionary<string, object>)configuration)["Username"].ToString();
            Password = ((IDictionary<string, object>)configuration)["Password"].ToString();
            FromAddress = ((IDictionary<string, object>)configuration)["FromAddress"].ToString();
            FromName = ((IDictionary<string, object>)configuration)["FromName"].ToString();
            EnableSSL = Boolean.Parse(((IDictionary<string, object>)configuration)["UseSSL"].ToString());
        }

        public Task Notify(List<string> toList, string subject, string body, byte[] byteArray = null)
        {
            MailMessage message = new MailMessage();
            SmtpClient smtp = new SmtpClient();
            foreach (var to in toList)
            {
                message.To.Add(new MailAddress(to));
            }
            message.Subject = subject;
            message.IsBodyHtml = true;
            message.Body = body;
            message.From = new MailAddress(FromAddress, FromName);
            smtp.Host = Server;
            smtp.Port = Port;
            smtp.EnableSsl = EnableSSL;
            smtp.UseDefaultCredentials = false;
            smtp.Credentials = new NetworkCredential(Username, Password);
            smtp.DeliveryMethod = SmtpDeliveryMethod.Network;

            if (byteArray != null)
            {
                using (MemoryStream stream = new MemoryStream(byteArray))
                {
                    Attachment attachment = new Attachment(stream, "attactment.pdf");
                    message.Attachments.Add(attachment);
                    smtp.Send(message);
                }
            }
            else
            {
                smtp.Send(message);
            }


            return Task.FromResult(0);
        }

        public string GetHelp()
        {
            return "";
        }
    }
}