using QuestPDF.Fluent;
using QuestPDF.Infrastructure;
using QuestPDF.Drawing;
using Svg.Skia;
using Factory.Plugins;
using Factory.Models;
using System.Reflection;
using System.Dynamic;
using QuestPDF.Helpers;
using SkiaSharp;
using static Factory.LicenseCertificateCredAiPlugin.Mapper;

namespace Factory.LicenseCertificateCredAiPlugin
{
    public class LicenseCertificateCredAiPlugin : IRemoteCallPlugin
    {
        public string GetName()
        {
            return "LicenseCertificateCredAi";
        }

        public List<string> GetMethods()
        {
            return new List<string>() {
                "Generate"
            };
        }

        public string GetHelp()
        {
            return "";
        }

        public List<FieldMeta> GetConfigurationMeta()
        {
            return new List<FieldMeta>() { };
        }



        public void Configure(dynamic configuration)
        {
        }

        public Task<dynamic> Invoke(dynamic input)
        {

            dynamic output = new ExpandoObject();
            output.Result = new LicenseCertificateCredAi(Mapper.Map<Model>(input)).GeneratePdf();
            return Task.FromResult<dynamic>(output);
        }
    }

    public class LicenseCertificateCredAi : IDocument
    {
        static LicenseCertificateCredAi()
        {
            var mangalFont = Assembly.GetExecutingAssembly().GetManifestResourceStream("Factory.LicenseCertificateCredAiPlugin.DVOTYOGESH_B_SHIP.TTF");
            if (mangalFont != null)
            {
                FontManager.RegisterFont(mangalFont);



            }
            var timesFont = Assembly.GetExecutingAssembly().GetManifestResourceStream("Factory.LicenseCertificateCredAiPlugin.times.ttf");
            if (timesFont != null)
            {
                FontManager.RegisterFont(timesFont);
            }
            var timesFontb = Assembly.GetExecutingAssembly().GetManifestResourceStream("Factory.LicenseCertificateCredAiPlugin.times-new-roman-grassetto.ttf");
            if (timesFontb != null)
            {
                FontManager.RegisterFont(timesFontb);
            }

            //  QuestPDF.Drawing.FontManager.RegisterFontWithCustomName("Times New Roman", File.OpenRead(@"D:\wwwroot\factoryplugins\factoryplugins\Factory.LicenseCertificateCredAiPlugin\times.ttf"));
        }



        public Model _model { get; }

        public LicenseCertificateCredAi(Model model)
        {
            _model = model;
        }


        public DocumentMetadata GetMetadata() => DocumentMetadata.Default;

        public void Compose(IDocumentContainer container)
        {
            container
                .Page(page =>
                {
                    page.Margin(35);
                    page.Header().Element(ComposeHeader);
                    page.Content().Element(ComposeContent);
                    page.Size(new PageSize(9, 15, Unit.Inch));
                    // page.Content().Element(ComposeTable);
                    // page.Content().Element(ComposeContent2);


                });
        }
        void ComposeHeader(IContainer container)
        {
            var stream = Assembly.GetExecutingAssembly().GetManifestResourceStream("Factory.LicenseCertificateCredAiPlugin.Logo.jpg");
            var Passport = Assembly.GetExecutingAssembly().GetManifestResourceStream("Factory.LicenseCertificateCredAiPlugin.Passport.jpg");
            container.Column(column =>
            {

                if (stream != null)
                {
                    column.Item().Row(row =>
          {

              row.ConstantItem(100)
                   .AlignLeft()
                       //    .AlignMiddle()
                       .Height(95)
                      .Width(95)
                   .Image(new BinaryReader(stream).ReadBytes((int)stream.Length), ImageScaling.Resize);

              row.Spacing(0);
              row.RelativeItem()
      .AlignCenter()
      .Text(text =>
      {
          text.Line("पुणे महानगर प्रदेश विकास प्राधिकरण, पुणे")
               .FontFamily("DVOT-Yogesh")
               .LineHeight(0.8f)
              .FontSize(18);
          text.Line("नविन प्रशासकीय इमारत, आकुर्डी रेल्वे स्टेशन जवळ, आकुर्डी, पुणे - 410044")
              .FontFamily("DVOT-Yogesh")
               .LineHeight(1)
              .FontSize(9);
          text.Line("Pune Metropolitan Region Development Authority, Pune")
              .FontFamily("Times New Roman")
              .FontSize(12)
               .LineHeight(1)
              .Bold();
          text.Line("New Administrative Building, Near Akurdi Railway Station, Akurdi, Pune - 410144")
              .FontFamily("Times New Roman")
               .LineHeight(1)
              .FontSize(10);
          text.Span("Phone no. 020 - 27652934")
              .FontFamily("Times New Roman")
              .FontSize(10);
          text.Span("      Email:")
          .FontFamily("Times New Roman")
          .FontSize(10);
          text.Span("<EMAIL>")
          .Underline()
        .FontFamily("Times New Roman")
        .FontSize(10);


      });

              row.ConstantItem(100)
                     .AlignRight()
                     .Height(90)
                     .Width(90)
                     .Image(new BinaryReader(_model.ProfilePhoto).ReadBytes((int)_model.ProfilePhoto.Length), ImageScaling.Resize);
              column.Item().LineHorizontal(1).LineColor(Colors.Grey.Darken1);
          });
                }

                // column.Item()
                //  .PaddingLeft(15)
                //  .PaddingRight(15)
                //  .AlignCenter()
                //  .AlignMiddle()
                //  .Text(text =>
                //  {

                //      text.Span("पुणे महानगर प्रदेश विकास प्राधिकरण,पुणे").FontSize(8)
                //          .FontFamily("DVOT-Yogesh").FontSize(9).Bold();


                //  });

                //  column.Item()
                //  .PaddingLeft(15)
                //  .PaddingRight(15)
                //  .AlignCenter()
                //  .AlignMiddle()
                //  .Text(text =>
                //  {

                //      text.Span("नविन प्रशासकीय इमारत, आकुर्डी रेल्वे स्टेशन जवळ, आकुर्डी, पुणे - 410044").FontSize(8)
                //          .FontFamily("DVOT-Yogesh").FontSize(9).Bold();


                //  });

                //   column.Item()
                //  .PaddingLeft(15)
                //  .PaddingRight(15)
                //  .AlignCenter()
                //  .AlignMiddle()
                //  .Text(text =>
                //  {

                //      text.Span("Pune Metropolitan Region Development Authority, Pune").FontSize(8)
                //          .FontFamily("Times New Roman");


                //  });

                //   column.Item()
                //  .PaddingLeft(15)
                //  .PaddingRight(15)
                //  .AlignCenter()
                //  .AlignMiddle()
                //  .Text(text =>
                //  {

                //      text.Span("New Administrative Building, Near Akurdi Railway Station, Akurdi, Pune - 410144").FontSize(8)
                //          .FontFamily("Times New Roman");


                //  });
                //   column.Item()
                //  .PaddingLeft(15)
                //  .PaddingRight(15)
                //  .AlignCenter()
                //  .Text(text =>
                //  {

                //      text.Span("Phone no. 020 - 27652934        Email: <EMAIL>").FontSize(8)
                //          .FontFamily("Times New Roman");

                //  });


            });
        }
        void ComposeContent(IContainer container)
        {
            container.Column(column =>
          {
              column.Item()
            .AlignCenter()
              .Text(text =>
              {
                  text.Span("CERTIFICATE").Underline().FontSize(14)
               .FontFamily("Times New Roman")
               .FontSize(16).Bold();
              });

              column.Item()
            .AlignLeft()
            .PaddingLeft(15)
            .PaddingRight(15)
              .Text(text =>
              {
                  text.Span("(For Licenced Technical Personnel for preparation of Schemes for development permission and Supervision) Certificate issued in accordance with Regulation 6.4 & Appendix ‘C’ of the Standardized Building Development Control and promotion regulations Rules for Pune Metropolitan Regional Development Authority area sanctioned by Government of Maharashtra.").FontSize(12)
               .FontFamily("Times New Roman");

              });
              column.Item()
          .AlignCenter()
          .PaddingLeft(15)
          .PaddingRight(15)
            .Text(text =>
            {
                text.Span($"LICENSE NO:- {_model.LicenseNo}{Environment.NewLine}").FontSize(14)
             .FontFamily("Times New Roman")
              .FontSize(14).Bold();

            });
              column.Item()
          .AlignLeft()
          .PaddingLeft(15)
          .PaddingRight(15)
            .Text(text =>
            {
                text.Span($"License is hereby granted to").FontSize(12)
             .FontFamily("Times New Roman")
              .FontSize(12);
                text.Span($" {_model.Name} ")
                .FontFamily("Times New Roman")
               .FontSize(12).Bold();
                text.Span($", Address: ")
               .FontFamily("Times New Roman")
              .FontSize(12).Bold();
                text.Span($" {_model.Address}")
               .FontFamily("Times New Roman")
              .FontSize(12).Bold();
                text.Span($" inresponse tohis application dated tocarryout work as Engineer inpursuanceof Regulation No.6.1 of DCPR as per scopeof Competence, Duties & Responsibilities and conditions as mentioned below.")
               .FontFamily("Times New Roman")
              .FontSize(12);

            });
              column.Item()
             .Column(column =>
    {



        column.Item()
       .Border(1)
       .Padding(3)
       .Column(nestedColumn =>
       {
           nestedColumn
               .Item()
               .Text("Competence-")
               .FontSize(12)
                .FontFamily("Times New Roman")
               .Bold();

           nestedColumn
              .Item()
              .Text(@"To carry out work related to development permission as given below and shall be entitled to submit-
(a) All plans and related information connected with development permission.
(b) Structural details and calculations for building on plot up to 500 sq.m. and upto 3 storeys or 10 m height, and 
(c) Certificate of supervision and completion for all building")
              .FontFamily("Times New Roman")
              .FontSize(12);

       });

    });
              column.Item()
          .Column(column =>
 {
     column.Item()
    .Border(1)
    .Padding(3)
    .Column(nestedColumn =>
    {
        nestedColumn
               .Item()
               .Text("Duties and Responsibilities of Architects / Licensed Technical Personnel:-")
               .FontSize(12)
                .FontFamily("Times New Roman")
               .Bold();

        nestedColumn
              .Item()
              .Text(text =>
              {
                  text.Span(@"The duties and responsibilities of architects / licensed technical personnel shall be as follows:-
(1) It will be incumbent on every architect / licensed technical personnel, in all matters in which he may be professionally consulted or engaged, to assist and co-operate with the Metropolitan Commissioner and 
other Officers in carrying out and enforcing the provisions of Maharashtra Regional & Town Planning 
Act, 1966 and of any regulations for the time being in force under the same. 
(2) Every architect/licensed technical personnel shall be responsible for due compliance with the 
provisions of Maharashtra Regional & Town Planning Act, 1966 and of any regulations for the time being 
in force under the said Act. It shall be obligatory on him to satisfy himself that a qualified and competent 
Misty or Inspector of Works is constantly employed and present on the work to supervise the execution of 
all work and to prevent the use of any defective material therein and the improper execution of any such work. 
(3) Every architect/licensed technical personnel shall be responsible for carrying out work according to sanctioned plan.
(4) Every architect/licensed technical personnel shall be responsible for correctness of the calculations and 
dimensions mentioned on the plan and shall be liable for consequences arising thereof. 
(5) Architect/licensed technical personnel shall not involve himself in any un authorized development. He 
shall also make aware the client about legal provisions in respect of proposed development and consequences thereof. 
(6) When an architect/licensed technical personnel cease to be in the employment for the development 
work, he shall report the fact forthwith to the ")
               .FontFamily("Times New Roman")
              .FontSize(12);
                  text.Span("Metropolitan Commissioner.")
            .FontFamily("Times New Roman")
           .FontSize(12).Bold();
              });

    });

 });

              column.Item()
            .AlignLeft()
            .PaddingLeft(15)
            .PaddingRight(15)
              .Text(text =>
              {
                  text.Span($@"The registration is granted subject to the conditions mentioned below..,
1) This registration is being issued based on documents submitted by applicant along with license 
application. If any mischief /false statement /misrepresentation found in documents /application 
submitted, authority shall take action as per regulation 10.1(c) of DCPR.
2) On every plan submitted to this office, it will be mandatory to mention registration number & 
validity (in case of architect, valid COA no. & validity).
3) The licensee shall produce the registration certificate as & when demanded by authority or person authorised.
4) The licensee shall apply for renewal 15 days prior to the expiry of registration.
 This License is valid upto Dt.{_model.ValidityDate}{Environment.NewLine}").FontSize(12)
               .FontFamily("Times New Roman");
              });



              {
                  column.Item().PaddingLeft(15).PaddingRight(15).Row(row =>
        {




            row.RelativeItem(3)
                .AlignLeft()
                .PaddingBottom(5)
                .Width(2 * 40) // sizes from 80x40 to 240x120
                .Height(4 * 20)
                .Scale(2)
                .Canvas((canvas, space) =>
                   {
                       Console.WriteLine("=========================>STARTED2");
                       using var svg = new SKSvg();

                       try
                       {
                           svg.FromSvg(_model.QrCodeUrl);
                           Console.WriteLine("=========================>STARTED3");
                           canvas.DrawPicture(svg.Picture);
                       }
                       catch (Exception ex)
                       {
                           Console.WriteLine($"EXCEPTION=========================>{ex.Message}");
                       }
                       Console.WriteLine("=========================>STARTED4");
                   });



            row.RelativeItem(5)
             .AlignRight()
             //.AlignMiddle()
             .Text(text =>
          {
              text.Line($"{Environment.NewLine}Metropolitan Planner, Development Permission")
                     .FontFamily("Times New Roman")
                     .FontSize(12).Bold();
              text.Line("Pune Metropolitan Regional Development Authority,Pune")
                    .FontFamily("Times New Roman")
                     .FontSize(12).Bold();

          });



        });
              }

              //       column.Item()
              //   .AlignRight()
              //   .PaddingLeft(20)
              //   .PaddingRight(20)
              //     .Text(text =>
              //     {
              //         text.Span($"{Environment.NewLine}Metropolitan Planner, Development Permission").FontSize(10)
              //      .FontFamily("Times New Roman")
              //      .FontSize(10).Bold();
              //     });
              //       column.Item()
              //   .AlignRight()
              //   .PaddingLeft(15)
              //   .PaddingRight(15)
              //     .Text(text =>
              //     {
              //         text.Span($"Pune Metropolitan Regional Development Authority,Pune").FontSize(10)
              //      .FontFamily("Times New Roman")
              //      .FontSize(10).Bold();
              //     });
              //       column.Item().Row(row =>
              //                                                           {
              //                                                               column
              //                                                   .Item()
              //                                                   .AlignLeft()
              //                                                     .PaddingBottom(5)
              //                                                   //   .Padding(1)
              //                                                   .Width(2 * 20) // sizes from 80x40 to 240x120
              //                                                   .Height(4 * 10)
              //                                                   .Scale(2)
              //                                                   .Canvas((canvas, space) =>
              //                                                      {
              //                                                          Console.WriteLine("=========================>STARTED2");
              //                                                          using var svg = new SKSvg();

              //                                                          try
              //                                                          {
              //                                                              svg.FromSvg(_model.QrCodeUrl);
              //                                                              Console.WriteLine("=========================>STARTED3");
              //                                                              canvas.DrawPicture(svg.Picture);
              //                                                          }
              //                                                          catch (Exception ex)
              //                                                          {
              //                                                              Console.WriteLine($"EXCEPTION=========================>{ex.Message}");
              //                                                          }
              //                                                          Console.WriteLine("=========================>STARTED4");
              //                                                      });
              //                                                               Console.WriteLine("=========================>STARTED5");
              //                                                           }
              //                                                           );
          });

        }
    }

    public static class Mapper
    {
        public static T Map<T>(ExpandoObject source) where T : class
        {
            T destination = (T)Activator.CreateInstance(typeof(T));
            IDictionary<string, object> dict = source;
            var type = destination.GetType();

            foreach (var prop in type.GetProperties())
            {
                var lower = prop.Name.ToLower();
                Console.WriteLine($"Mapping {lower}");
                var key = dict.Keys.SingleOrDefault(k => k.ToLower() == lower);

                if (key != null)
                {
                    prop.SetValue(destination, CastProperty(dict[key], prop.PropertyType.ToString()), null);
                }
            }
            return destination;
        }

        static dynamic CastProperty(dynamic property, string type)
        {
            switch (type)
            {
                case "System.String":
                    return (String)property;
                case "System.Decimal":
                    return (Decimal)property;
                default:
                    return property;

            }
        }
        public class Model
        {

            public dynamic ProfilePhoto { get; set; }
            public string LicenseNo { get; set; }
            public string ValidityDate { get; set; }
            public string Name { get; set; }
            public string QrCodeUrl { get; set; }
            public string Address { get; set; }
        }


    }
}
